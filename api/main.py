"""
🧠 CrewAI OSINT Agent Framework - FastAPI REST API

REST API endpoints for external integration with the OSINT framework.
Provides programmatic access to all agent capabilities.
"""

from fastapi import FastAP<PERSON>, HTTPException, BackgroundTasks, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
import sys
import os
from pathlib import Path
from datetime import datetime
import json
import asyncio
import uuid

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from agents.geo_agent import GeopoliticalAgent
from agents.cti_agent import CTIAgent
from agents.stateful_geo_agent import StatefulGeopoliticalAgent
from agents.stateful_cti_agent import StatefulCTIAgent
from agents.multimodal_osint_agent import MultiModalOSINTAgent
from agents.browser_osint_agent import BrowserOSINTAgent
from workflows.workflow_engine import workflow_engine, WorkflowDefinition, WorkflowTask, TaskType
from workflows.workflow_builder import WorkflowBuilder, WorkflowTemplates
from workflows.scheduler import workflow_scheduler, TriggerType
from workflows.langchain_geo_workflow import GeopoliticalWorkflow
from workflows.llamaindex_cti_workflow import CTIWorkflow
from rag.index_builder import IndexBuilder
from rag.retriever import DocumentRetriever
from utils.monitoring import health_checker, metrics_collector, alert_manager, start_monitoring
from utils.logging_config import osint_logger, log_api_call, performance_monitor
from utils.security import auth_manager, encryption_manager, privacy_manager, local_embeddings

# FastAPI app initialization
app = FastAPI(
    title="🧠 OSINT Agent Framework API",
    description="REST API for the CrewAI OSINT Agent Framework",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models for request/response
class GeopoliticalAnalysisRequest(BaseModel):
    topic: str = Field(..., description="Analysis topic")
    regions: List[str] = Field(..., description="Regions of interest")
    time_range: str = Field(default="30d", description="Time range for analysis")
    analysis_type: str = Field(default="intelligence_brief", description="Type of analysis")

class CTIAnalysisRequest(BaseModel):
    text_content: str = Field(..., description="Text content to analyze")
    analysis_type: str = Field(default="ioc_extraction", description="Type of CTI analysis")
    context: Optional[str] = Field(None, description="Additional context")

class DocumentIndexRequest(BaseModel):
    documents: Optional[List[Dict[str, Any]]] = Field(None, description="Documents to index")
    urls: Optional[List[str]] = Field(None, description="URLs to crawl and index")
    text_content: Optional[str] = Field(None, description="Text content to index")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")

class SearchRequest(BaseModel):
    query: str = Field(..., description="Search query")
    search_type: str = Field(default="semantic", description="Type of search")
    max_results: int = Field(default=5, description="Maximum number of results")
    filters: Optional[Dict[str, Any]] = Field(None, description="Search filters")

class AnalysisResponse(BaseModel):
    task_id: str
    status: str
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    timestamp: str

class ConversationRequest(BaseModel):
    user_id: str = Field(default="default", description="User identifier")
    agent_type: str = Field(..., description="Agent type (geopolitical or cti)")
    initial_context: Optional[Dict[str, Any]] = Field(None, description="Initial conversation context")

class ConversationMessageRequest(BaseModel):
    conversation_id: str = Field(..., description="Conversation ID")
    content: str = Field(..., description="Message content")
    analysis_type: Optional[str] = Field(None, description="Type of analysis to perform")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")

class ConversationResponse(BaseModel):
    conversation_id: str
    status: str
    result: Optional[Dict[str, Any]] = None
    context: Optional[Dict[str, Any]] = None
    timestamp: str

class MultiModalAnalysisRequest(BaseModel):
    media_type: str = Field(default="auto", description="Media type (image, video, auto)")
    analysis_types: Optional[List[str]] = Field(None, description="Types of analysis to perform")
    osint_context: bool = Field(default=True, description="Include OSINT enrichment")
    user_id: str = Field(default="default", description="User identifier")

class BrowserInvestigationRequest(BaseModel):
    target: str = Field(..., description="Target to investigate")
    investigation_type: str = Field(default="comprehensive", description="Type of investigation")
    use_stealth: bool = Field(default=True, description="Use stealth browser mode")
    use_playwright: bool = Field(default=False, description="Use Playwright for advanced features")
    user_id: str = Field(default="default", description="User identifier")

class AuthRequest(BaseModel):
    username: str = Field(..., description="Username")
    password: str = Field(..., description="Password")

class CreateUserRequest(BaseModel):
    username: str = Field(..., description="Username")
    password: str = Field(..., description="Password")
    permissions: Optional[List[str]] = Field(default=["read"], description="User permissions")

class EncryptionRequest(BaseModel):
    data: Union[str, Dict[str, Any]] = Field(..., description="Data to encrypt")

class DecryptionRequest(BaseModel):
    encrypted_data: str = Field(..., description="Encrypted data to decrypt")

class WorkflowExecutionRequest(BaseModel):
    workflow_id: str = Field(..., description="Workflow ID to execute")
    initial_context: Optional[Dict[str, Any]] = Field(None, description="Initial execution context")

class WorkflowScheduleRequest(BaseModel):
    workflow_id: str = Field(..., description="Workflow ID to schedule")
    trigger_type: str = Field(..., description="Trigger type (schedule, event, webhook)")
    schedule: Optional[str] = Field(None, description="Cron schedule expression")
    event_type: Optional[str] = Field(None, description="Event type to listen for")
    webhook_path: Optional[str] = Field(None, description="Webhook path")
    context: Optional[Dict[str, Any]] = Field(None, description="Execution context")
    max_executions: Optional[int] = Field(None, description="Maximum number of executions")

class WorkflowTemplateRequest(BaseModel):
    template_name: str = Field(..., description="Template name")
    parameters: Dict[str, Any] = Field(..., description="Template parameters")

# In-memory task storage (use Redis/database in production)
tasks = {}

# Authentication dependency
def get_current_user(authorization: str = Header(None)):
    """Get current authenticated user"""

    if not authorization:
        raise HTTPException(status_code=401, detail="Authorization header required")

    if not authorization.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="Invalid authorization header format")

    session_id = authorization.split(" ")[1]

    if not auth_manager:
        # If auth manager not available, allow access (development mode)
        return {"user_id": "dev_user", "permissions": ["admin"]}

    session = auth_manager.validate_session(session_id)

    if not session:
        raise HTTPException(status_code=401, detail="Invalid or expired session")

    return {
        "user_id": session.user_id,
        "session_id": session.session_id,
        "permissions": session.permissions
    }

def require_permission(permission: str):
    """Decorator to require specific permission"""

    def permission_checker(current_user: dict = Depends(get_current_user)):
        if "admin" not in current_user["permissions"] and permission not in current_user["permissions"]:
            raise HTTPException(status_code=403, detail=f"Permission '{permission}' required")
        return current_user

    return permission_checker

# Startup and shutdown events
@app.on_event("startup")
async def startup_event():
    """Initialize monitoring and logging on startup"""
    osint_logger.logger.info("Starting OSINT Framework API")
    start_monitoring()

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    osint_logger.logger.info("Shutting down OSINT Framework API")
    from utils.monitoring import stop_monitoring
    stop_monitoring()

# Helper functions
def create_task(task_type: str, request_data: Dict[str, Any]) -> str:
    """Create a new analysis task"""
    task_id = str(uuid.uuid4())
    tasks[task_id] = {
        "id": task_id,
        "type": task_type,
        "status": "pending",
        "request_data": request_data,
        "result": None,
        "error": None,
        "created_at": datetime.now().isoformat(),
        "updated_at": datetime.now().isoformat()
    }
    return task_id

def update_task(task_id: str, status: str, result: Any = None, error: str = None):
    """Update task status"""
    if task_id in tasks:
        tasks[task_id].update({
            "status": status,
            "result": result,
            "error": error,
            "updated_at": datetime.now().isoformat()
        })

# API Routes

@app.get("/")
async def root():
    """API root endpoint"""
    return {
        "message": "🧠 OSINT Agent Framework API",
        "version": "1.0.0",
        "docs": "/docs",
        "status": "operational"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return await health_checker.run_health_checks()

@app.get("/metrics")
async def get_metrics():
    """Get system and application metrics"""
    try:
        current_metrics = metrics_collector.collect_metrics()
        summary = metrics_collector.get_metrics_summary(hours=1)

        return {
            "current": current_metrics,
            "summary": summary,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        osint_logger.log_error(e, "metrics_endpoint")
        raise HTTPException(status_code=500, detail="Failed to collect metrics")

@app.get("/monitoring/dashboard")
async def monitoring_dashboard():
    """Get comprehensive monitoring dashboard data"""
    try:
        # Get health status
        health_status = await health_checker.run_health_checks()

        # Get metrics
        current_metrics = metrics_collector.collect_metrics()
        metrics_summary = metrics_collector.get_metrics_summary(hours=24)

        # Get performance stats
        perf_stats = performance_monitor.get_performance_stats()

        # Get active alerts
        active_alerts = list(alert_manager.active_alerts.values())

        return {
            "health": health_status,
            "metrics": {
                "current": current_metrics,
                "summary": metrics_summary
            },
            "performance": perf_stats,
            "alerts": {
                "active": active_alerts,
                "count": len(active_alerts)
            },
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        osint_logger.log_error(e, "dashboard_endpoint")
        raise HTTPException(status_code=500, detail="Failed to get dashboard data")

@app.get("/monitoring/alerts")
async def get_alerts():
    """Get current alerts"""
    return {
        "active_alerts": list(alert_manager.active_alerts.values()),
        "alert_history": alert_manager.alert_history[-50:],  # Last 50 alerts
        "timestamp": datetime.now().isoformat()
    }

# Geopolitical Analysis Endpoints

@app.post("/api/v1/geopolitical/analyze", response_model=AnalysisResponse)
async def analyze_geopolitical(request: GeopoliticalAnalysisRequest, background_tasks: BackgroundTasks):
    """Start geopolitical analysis"""
    
    if not (os.getenv('OPENAI_API_KEY') and os.getenv('SERPER_API_KEY')):
        raise HTTPException(status_code=400, detail="API keys not configured")
    
    task_id = create_task("geopolitical_analysis", request.dict())
    
    # Add background task
    background_tasks.add_task(run_geopolitical_analysis, task_id, request)
    
    return AnalysisResponse(
        task_id=task_id,
        status="pending",
        timestamp=datetime.now().isoformat()
    )

async def run_geopolitical_analysis(task_id: str, request: GeopoliticalAnalysisRequest):
    """Run geopolitical analysis in background"""
    try:
        update_task(task_id, "running")
        
        geo_agent = GeopoliticalAgent()
        
        if request.analysis_type == "intelligence_brief":
            result = geo_agent.generate_intelligence_brief(
                topic=request.topic,
                regions=request.regions,
                time_range=request.time_range
            )
        elif request.analysis_type == "regional_monitoring":
            result = geo_agent.setup_regional_monitoring(
                regions=request.regions,
                keywords=[],
                frequency="daily"
            )
        else:
            workflow = GeopoliticalWorkflow()
            result = workflow.analyze_geopolitical_situation(
                topic=request.topic,
                regions=request.regions,
                time_range=request.time_range
            )
        
        update_task(task_id, "completed", result)
        
    except Exception as e:
        update_task(task_id, "failed", error=str(e))

@app.post("/api/v1/geopolitical/situation-report", response_model=AnalysisResponse)
async def generate_situation_report(
    regions: List[str],
    time_range: str = "24h",
    background_tasks: BackgroundTasks = None
):
    """Generate multi-region situation report"""
    
    if not (os.getenv('OPENAI_API_KEY') and os.getenv('SERPER_API_KEY')):
        raise HTTPException(status_code=400, detail="API keys not configured")
    
    task_id = create_task("situation_report", {"regions": regions, "time_range": time_range})
    
    if background_tasks:
        background_tasks.add_task(run_situation_report, task_id, regions, time_range)
    
    return AnalysisResponse(
        task_id=task_id,
        status="pending",
        timestamp=datetime.now().isoformat()
    )

async def run_situation_report(task_id: str, regions: List[str], time_range: str):
    """Run situation report generation in background"""
    try:
        update_task(task_id, "running")
        
        workflow = GeopoliticalWorkflow()
        result = workflow.generate_situation_report(regions=regions, time_range=time_range)
        
        update_task(task_id, "completed", result)
        
    except Exception as e:
        update_task(task_id, "failed", error=str(e))

# CTI Analysis Endpoints

@app.post("/api/v1/cti/analyze", response_model=AnalysisResponse)
async def analyze_cti(request: CTIAnalysisRequest, background_tasks: BackgroundTasks):
    """Start CTI analysis"""
    
    if not (os.getenv('OPENAI_API_KEY') and os.getenv('SERPER_API_KEY')):
        raise HTTPException(status_code=400, detail="API keys not configured")
    
    task_id = create_task("cti_analysis", request.dict())
    
    # Add background task
    background_tasks.add_task(run_cti_analysis, task_id, request)
    
    return AnalysisResponse(
        task_id=task_id,
        status="pending",
        timestamp=datetime.now().isoformat()
    )

async def run_cti_analysis(task_id: str, request: CTIAnalysisRequest):
    """Run CTI analysis in background"""
    try:
        update_task(task_id, "running")
        
        cti_agent = CTIAgent()
        
        if request.analysis_type == "ioc_extraction":
            result = cti_agent.extract_iocs(request.text_content)
        elif request.analysis_type == "threat_actor_tracking":
            # Extract potential threat actor names from text
            result = cti_agent.track_threat_actor("Unknown")  # Would need NER here
        elif request.analysis_type == "campaign_analysis":
            result = cti_agent.correlate_campaign_data(request.text_content, ["IOC Correlation"])
        else:
            result = {"error": "Unknown analysis type"}
        
        update_task(task_id, "completed", result)
        
    except Exception as e:
        update_task(task_id, "failed", error=str(e))

@app.post("/api/v1/cti/extract-iocs")
async def extract_iocs_sync(text_content: str):
    """Extract IOCs synchronously (for quick results)"""
    
    if not os.getenv('OPENAI_API_KEY'):
        raise HTTPException(status_code=400, detail="OpenAI API key not configured")
    
    try:
        cti_agent = CTIAgent()
        iocs = cti_agent.extract_iocs(text_content)
        
        return {
            "iocs": iocs,
            "count": len(iocs),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Document Management Endpoints

@app.post("/api/v1/documents/index", response_model=AnalysisResponse)
async def index_documents(request: DocumentIndexRequest, background_tasks: BackgroundTasks):
    """Index documents for RAG"""
    
    task_id = create_task("document_indexing", request.dict())
    
    # Add background task
    background_tasks.add_task(run_document_indexing, task_id, request)
    
    return AnalysisResponse(
        task_id=task_id,
        status="pending",
        timestamp=datetime.now().isoformat()
    )

async def run_document_indexing(task_id: str, request: DocumentIndexRequest):
    """Run document indexing in background"""
    try:
        update_task(task_id, "running")
        
        index_builder = IndexBuilder()
        
        index = index_builder.build_index(
            documents=request.documents,
            urls=request.urls,
            text_content=request.text_content
        )
        
        result = {
            "index_created": True,
            "document_count": len(request.documents or []),
            "url_count": len(request.urls or []),
            "text_indexed": bool(request.text_content)
        }
        
        update_task(task_id, "completed", result)
        
    except Exception as e:
        update_task(task_id, "failed", error=str(e))

@app.post("/api/v1/documents/search")
async def search_documents(request: SearchRequest):
    """Search indexed documents"""
    
    try:
        retriever = DocumentRetriever()
        
        results = retriever.search(
            query=request.query,
            search_type=request.search_type,
            max_results=request.max_results
        )
        
        return {
            "results": results,
            "count": len(results),
            "query": request.query,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Task Management Endpoints

@app.get("/api/v1/tasks/{task_id}", response_model=AnalysisResponse)
async def get_task_status(task_id: str):
    """Get task status and results"""
    
    if task_id not in tasks:
        raise HTTPException(status_code=404, detail="Task not found")
    
    task = tasks[task_id]
    
    return AnalysisResponse(
        task_id=task_id,
        status=task["status"],
        result=task["result"],
        error=task["error"],
        timestamp=task["updated_at"]
    )

@app.get("/api/v1/tasks")
async def list_tasks(limit: int = 10, status: Optional[str] = None):
    """List recent tasks"""
    
    task_list = list(tasks.values())
    
    # Filter by status if provided
    if status:
        task_list = [t for t in task_list if t["status"] == status]
    
    # Sort by creation time (newest first)
    task_list.sort(key=lambda x: x["created_at"], reverse=True)
    
    # Limit results
    task_list = task_list[:limit]
    
    return {
        "tasks": task_list,
        "count": len(task_list),
        "total": len(tasks)
    }

# File Upload Endpoints

@app.post("/api/v1/upload/analyze")
async def upload_and_analyze(
    file: UploadFile = File(...),
    analysis_type: str = "auto",
    background_tasks: BackgroundTasks = None
):
    """Upload file and analyze content"""
    
    try:
        # Read file content
        content = await file.read()
        text_content = content.decode('utf-8')
        
        # Determine analysis type based on content or file type
        if analysis_type == "auto":
            if any(keyword in text_content.lower() for keyword in ["ioc", "malware", "threat", "apt"]):
                analysis_type = "cti"
            else:
                analysis_type = "geopolitical"
        
        # Create analysis request
        if analysis_type == "cti":
            request = CTIAnalysisRequest(
                text_content=text_content,
                analysis_type="ioc_extraction"
            )
            task_id = create_task("cti_analysis", request.dict())
            if background_tasks:
                background_tasks.add_task(run_cti_analysis, task_id, request)
        else:
            # For geopolitical, we'd need to extract topic and regions
            # This is a simplified version
            task_id = create_task("file_analysis", {
                "filename": file.filename,
                "content_length": len(text_content),
                "analysis_type": analysis_type
            })
        
        return AnalysisResponse(
            task_id=task_id,
            status="pending",
            timestamp=datetime.now().isoformat()
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Stateful Conversation Endpoints

@app.post("/api/v1/conversations/start", response_model=ConversationResponse)
async def start_conversation(request: ConversationRequest):
    """Start a new stateful conversation"""

    try:
        if request.agent_type == "geopolitical":
            agent = StatefulGeopoliticalAgent(user_id=request.user_id)
        elif request.agent_type == "cti":
            agent = StatefulCTIAgent(user_id=request.user_id)
        else:
            raise HTTPException(status_code=400, detail="Invalid agent type")

        conversation_id = agent.start_conversation(request.initial_context)

        return ConversationResponse(
            conversation_id=conversation_id,
            status="started",
            context=agent.get_conversation_context(),
            timestamp=datetime.now().isoformat()
        )

    except Exception as e:
        osint_logger.log_error(e, "conversation_start")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/conversations/message", response_model=ConversationResponse)
async def send_message(request: ConversationMessageRequest, background_tasks: BackgroundTasks):
    """Send a message and get analysis"""

    try:
        # Load conversation to determine agent type
        from utils.memory_manager import memory_manager
        conversation = memory_manager.get_conversation(request.conversation_id)

        if not conversation:
            raise HTTPException(status_code=404, detail="Conversation not found")

        # Create appropriate agent
        if conversation.agent_type == "geopolitical":
            agent = StatefulGeopoliticalAgent(user_id=conversation.user_id)
            agent.continue_conversation(request.conversation_id)

            # Perform analysis
            result = agent.analyze_with_context(
                query=request.content,
                analysis_type=request.analysis_type or "intelligence_brief"
            )
        elif conversation.agent_type == "cti":
            agent = StatefulCTIAgent(user_id=conversation.user_id)
            agent.continue_conversation(request.conversation_id)

            # Perform analysis
            result = agent.analyze_threat_with_context(
                content=request.content,
                analysis_type=request.analysis_type or "ioc_extraction"
            )
        else:
            raise HTTPException(status_code=400, detail="Invalid agent type")

        return ConversationResponse(
            conversation_id=request.conversation_id,
            status="completed",
            result=result,
            context=agent.get_conversation_context(),
            timestamp=datetime.now().isoformat()
        )

    except Exception as e:
        osint_logger.log_error(e, "conversation_message")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/conversations/{conversation_id}")
async def get_conversation(conversation_id: str):
    """Get conversation details and history"""

    try:
        from utils.memory_manager import memory_manager
        conversation = memory_manager.get_conversation(conversation_id)

        if not conversation:
            raise HTTPException(status_code=404, detail="Conversation not found")

        # Get conversation history
        history = memory_manager.get_conversation_history(conversation_id, limit=100)

        # Get relevant memories
        memories = memory_manager.get_relevant_memories(conversation_id, limit=20)

        return {
            "conversation_id": conversation_id,
            "user_id": conversation.user_id,
            "agent_type": conversation.agent_type,
            "created_at": conversation.created_at,
            "updated_at": conversation.updated_at,
            "active": conversation.active,
            "context": conversation.context,
            "message_history": history,
            "relevant_memories": [
                {
                    "content": memory.content[:200],  # Truncate for API
                    "content_type": memory.content_type,
                    "importance": memory.importance,
                    "timestamp": memory.timestamp,
                    "tags": memory.tags
                }
                for memory in memories
            ],
            "stats": {
                "message_count": len(conversation.messages),
                "memory_count": len(memories)
            }
        }

    except Exception as e:
        osint_logger.log_error(e, "get_conversation")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/conversations/{conversation_id}/export")
async def export_conversation(conversation_id: str):
    """Export complete conversation data"""

    try:
        from utils.memory_manager import memory_manager
        conversation = memory_manager.get_conversation(conversation_id)

        if not conversation:
            raise HTTPException(status_code=404, detail="Conversation not found")

        # Create appropriate agent for export
        if conversation.agent_type == "geopolitical":
            agent = StatefulGeopoliticalAgent(user_id=conversation.user_id)
        elif conversation.agent_type == "cti":
            agent = StatefulCTIAgent(user_id=conversation.user_id)
        else:
            raise HTTPException(status_code=400, detail="Invalid agent type")

        agent.continue_conversation(conversation_id)
        export_data = agent.export_conversation()

        return export_data

    except Exception as e:
        osint_logger.log_error(e, "export_conversation")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/conversations")
async def list_conversations(user_id: str = "default", limit: int = 20):
    """List conversations for a user"""

    try:
        from utils.memory_manager import memory_manager

        # This would need to be implemented in memory_manager
        # For now, return a placeholder
        return {
            "user_id": user_id,
            "conversations": [],
            "total": 0,
            "limit": limit,
            "message": "Conversation listing not yet implemented"
        }

    except Exception as e:
        osint_logger.log_error(e, "list_conversations")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/memory/stats")
async def get_memory_stats():
    """Get memory system statistics"""

    try:
        from utils.memory_manager import memory_manager
        stats = memory_manager.get_conversation_stats()

        return {
            "memory_stats": stats,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        osint_logger.log_error(e, "memory_stats")
        raise HTTPException(status_code=500, detail=str(e))

# Multi-Modal Analysis Endpoints

@app.post("/api/v1/multimodal/analyze-upload")
async def analyze_uploaded_media(
    file: UploadFile = File(...),
    request: MultiModalAnalysisRequest = None,
    background_tasks: BackgroundTasks = None
):
    """Analyze uploaded media file"""

    try:
        # Read file content
        content = await file.read()

        # Create multi-modal agent
        agent = MultiModalOSINTAgent(user_id=request.user_id if request else "default")

        # Determine analysis parameters
        media_type = request.media_type if request else "auto"
        analysis_types = request.analysis_types if request else None
        osint_context = request.osint_context if request else True

        # Perform analysis
        result = agent.analyze_media_with_context(
            media_input=content,
            media_type=media_type,
            analysis_types=analysis_types,
            osint_context=osint_context
        )

        # Add file metadata
        result["file_info"] = {
            "filename": file.filename,
            "content_type": file.content_type,
            "size_bytes": len(content)
        }

        return {
            "status": "completed",
            "result": result,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        osint_logger.log_error(e, "multimodal_upload_analysis")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/multimodal/analyze-url")
async def analyze_media_url(
    url: str,
    request: MultiModalAnalysisRequest = None
):
    """Analyze media from URL"""

    try:
        # Download media from URL
        response = requests.get(url, timeout=30)
        response.raise_for_status()

        content = response.content

        # Create multi-modal agent
        agent = MultiModalOSINTAgent(user_id=request.user_id if request else "default")

        # Determine analysis parameters
        media_type = request.media_type if request else "auto"
        analysis_types = request.analysis_types if request else None
        osint_context = request.osint_context if request else True

        # Perform analysis
        result = agent.analyze_media_with_context(
            media_input=content,
            media_type=media_type,
            analysis_types=analysis_types,
            osint_context=osint_context
        )

        # Add URL metadata
        result["source_info"] = {
            "url": url,
            "content_type": response.headers.get("content-type"),
            "size_bytes": len(content)
        }

        return {
            "status": "completed",
            "result": result,
            "timestamp": datetime.now().isoformat()
        }

    except requests.exceptions.RequestException as e:
        raise HTTPException(status_code=400, detail=f"Failed to download media: {str(e)}")
    except Exception as e:
        osint_logger.log_error(e, "multimodal_url_analysis")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/multimodal/batch-analyze")
async def batch_analyze_media(
    files: List[UploadFile] = File(...),
    analysis_types: Optional[List[str]] = None,
    user_id: str = "default"
):
    """Batch analyze multiple media files"""

    try:
        # Create multi-modal agent
        agent = MultiModalOSINTAgent(user_id=user_id)

        # Process files
        media_inputs = []
        file_info = []

        for file in files:
            content = await file.read()
            media_inputs.append(content)
            file_info.append({
                "filename": file.filename,
                "content_type": file.content_type,
                "size_bytes": len(content)
            })

        # Perform batch analysis
        batch_result = agent.batch_analyze_media(
            media_inputs,
            analysis_types=analysis_types
        )

        # Add file information
        for i, info in enumerate(file_info):
            if i < len(batch_result["results"]):
                batch_result["results"][i]["file_info"] = info

        return {
            "status": "completed",
            "batch_result": batch_result,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        osint_logger.log_error(e, "multimodal_batch_analysis")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/multimodal/capabilities")
async def get_multimodal_capabilities():
    """Get available multi-modal analysis capabilities"""

    try:
        from tools.multimodal_analyzer import (
            OPENCV_AVAILABLE, TESSERACT_AVAILABLE, EASYOCR_AVAILABLE,
            FACE_RECOGNITION_AVAILABLE
        )

        capabilities = {
            "image_analysis": {
                "supported_formats": [".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".webp"],
                "analysis_types": ["metadata", "ocr", "faces", "objects", "forensics"],
                "ocr_engines": {
                    "tesseract": TESSERACT_AVAILABLE,
                    "easyocr": EASYOCR_AVAILABLE
                },
                "face_recognition": FACE_RECOGNITION_AVAILABLE,
                "opencv": OPENCV_AVAILABLE
            },
            "video_analysis": {
                "supported_formats": [".mp4", ".avi", ".mov", ".mkv", ".wmv", ".flv"],
                "analysis_types": ["metadata", "frames", "motion"],
                "opencv": OPENCV_AVAILABLE
            },
            "osint_enrichment": {
                "text_search": True,
                "metadata_analysis": True,
                "geolocation": True,
                "security_assessment": True
            },
            "features": {
                "batch_processing": True,
                "conversation_context": True,
                "memory_persistence": True,
                "security_indicators": True
            }
        }

        return capabilities

    except Exception as e:
        osint_logger.log_error(e, "multimodal_capabilities")
        raise HTTPException(status_code=500, detail=str(e))

# Browser Automation OSINT Endpoints

@app.post("/api/v1/browser/investigate")
async def investigate_target(request: BrowserInvestigationRequest, background_tasks: BackgroundTasks):
    """Investigate a target using browser automation"""

    try:
        # Create browser OSINT agent
        agent = BrowserOSINTAgent(
            user_id=request.user_id,
            headless=True,  # Always headless in API
            stealth=request.use_stealth
        )

        # Perform investigation
        if request.use_playwright:
            # Use advanced Playwright investigation
            result = await agent.investigate_target_advanced(
                target=request.target,
                use_playwright=True
            )
        else:
            # Use standard Selenium investigation
            result = agent.investigate_target(
                target=request.target,
                investigation_type=request.investigation_type,
                use_stealth=request.use_stealth
            )

        return {
            "status": "completed",
            "investigation_result": result,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        osint_logger.log_error(e, "browser_investigation")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/browser/scrape-page")
async def scrape_page(
    url: str,
    extract_elements: Optional[List[str]] = None,
    use_stealth: bool = True,
    wait_time: int = 10
):
    """Scrape a specific page using browser automation"""

    try:
        from tools.browser_automation import BrowserAutomation

        with BrowserAutomation(headless=True, stealth=use_stealth) as browser:
            result = browser.scrape_page(
                url=url,
                wait_time=wait_time,
                extract_elements=extract_elements or []
            )

        return {
            "status": "completed",
            "scraping_result": result,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        osint_logger.log_error(e, "page_scraping")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/browser/interact")
async def interact_with_page(
    url: str,
    interactions: List[Dict[str, Any]],
    use_stealth: bool = True
):
    """Interact with a page using browser automation"""

    try:
        from tools.browser_automation import BrowserAutomation

        with BrowserAutomation(headless=True, stealth=use_stealth) as browser:
            # First navigate to the page
            browser.driver.get(url)

            # Perform interactions
            result = browser.interact_with_page(interactions)

        return {
            "status": "completed",
            "interaction_result": result,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        osint_logger.log_error(e, "page_interaction")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/browser/social-media-analysis")
async def analyze_social_media(
    platform: str,
    target: str,
    user_id: str = "default"
):
    """Analyze social media presence of a target"""

    try:
        # Create browser OSINT agent
        agent = BrowserOSINTAgent(user_id=user_id, headless=True, stealth=True)

        # Perform social media analysis
        result = agent._perform_social_media_analysis(target)

        # Filter for specific platform if requested
        if platform.lower() != "all":
            platform_result = result.get("platform_searches", {}).get(platform.lower(), {})
            profile_result = result.get("profile_analysis", {}).get(platform.lower(), {})

            filtered_result = {
                "platform": platform,
                "search_results": platform_result,
                "profile_analysis": profile_result
            }
        else:
            filtered_result = result

        return {
            "status": "completed",
            "social_media_analysis": filtered_result,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        osint_logger.log_error(e, "social_media_analysis")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/browser/capabilities")
async def get_browser_capabilities():
    """Get available browser automation capabilities"""

    try:
        from tools.browser_automation import (
            SELENIUM_AVAILABLE, PLAYWRIGHT_AVAILABLE, UNDETECTED_CHROME_AVAILABLE
        )

        capabilities = {
            "selenium": {
                "available": SELENIUM_AVAILABLE,
                "browsers": ["chrome", "firefox"],
                "features": ["basic_scraping", "element_interaction", "screenshots"]
            },
            "playwright": {
                "available": PLAYWRIGHT_AVAILABLE,
                "browsers": ["chromium", "firefox", "webkit"],
                "features": ["advanced_scraping", "network_monitoring", "dynamic_content"]
            },
            "undetected_chrome": {
                "available": UNDETECTED_CHROME_AVAILABLE,
                "features": ["stealth_mode", "anti_detection", "bot_evasion"]
            },
            "investigation_types": [
                "comprehensive",
                "social_media",
                "technical",
                "web_analysis"
            ],
            "supported_platforms": [
                "twitter",
                "linkedin",
                "facebook",
                "instagram",
                "youtube",
                "github"
            ],
            "features": {
                "stealth_browsing": True,
                "dynamic_content_extraction": PLAYWRIGHT_AVAILABLE,
                "social_media_analysis": True,
                "technical_analysis": True,
                "threat_indicator_detection": True,
                "conversation_context": True,
                "memory_persistence": True
            }
        }

        return capabilities

    except Exception as e:
        osint_logger.log_error(e, "browser_capabilities")
        raise HTTPException(status_code=500, detail=str(e))

# Security and Authentication Endpoints

@app.post("/api/v1/auth/login")
async def login(request: AuthRequest, request_info: Request):
    """Authenticate user and create session"""

    try:
        if not auth_manager:
            raise HTTPException(status_code=503, detail="Authentication service not available")

        # Get client info
        ip_address = request_info.client.host
        user_agent = request_info.headers.get("user-agent", "")

        # Authenticate user
        session_id = auth_manager.authenticate_user(
            username=request.username,
            password=request.password,
            ip_address=ip_address,
            user_agent=user_agent
        )

        if not session_id:
            raise HTTPException(status_code=401, detail="Invalid credentials")

        return {
            "status": "success",
            "session_id": session_id,
            "message": "Authentication successful"
        }

    except HTTPException:
        raise
    except Exception as e:
        osint_logger.log_error(e, "user_authentication")
        raise HTTPException(status_code=500, detail="Authentication failed")

@app.post("/api/v1/auth/logout")
async def logout(current_user: dict = Depends(get_current_user)):
    """Logout current session"""

    try:
        if auth_manager and "session_id" in current_user:
            auth_manager.logout_session(current_user["session_id"])

        return {
            "status": "success",
            "message": "Logout successful"
        }

    except Exception as e:
        osint_logger.log_error(e, "user_logout")
        raise HTTPException(status_code=500, detail="Logout failed")

@app.post("/api/v1/auth/create-user")
async def create_user(request: CreateUserRequest, current_user: dict = Depends(require_permission("admin"))):
    """Create a new user (admin only)"""

    try:
        if not auth_manager:
            raise HTTPException(status_code=503, detail="Authentication service not available")

        user_id = auth_manager.create_user(
            username=request.username,
            password=request.password,
            permissions=request.permissions
        )

        return {
            "status": "success",
            "user_id": user_id,
            "message": "User created successfully"
        }

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        osint_logger.log_error(e, "user_creation")
        raise HTTPException(status_code=500, detail="User creation failed")

@app.get("/api/v1/auth/profile")
async def get_profile(current_user: dict = Depends(get_current_user)):
    """Get current user profile"""

    return {
        "user_id": current_user["user_id"],
        "permissions": current_user["permissions"],
        "authenticated": True
    }

@app.post("/api/v1/security/encrypt")
async def encrypt_data(request: EncryptionRequest, current_user: dict = Depends(require_permission("write"))):
    """Encrypt sensitive data"""

    try:
        if not encryption_manager:
            raise HTTPException(status_code=503, detail="Encryption service not available")

        encrypted_data = encryption_manager.encrypt_data(request.data)

        return {
            "status": "success",
            "encrypted_data": encrypted_data,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        osint_logger.log_error(e, "data_encryption")
        raise HTTPException(status_code=500, detail="Encryption failed")

@app.post("/api/v1/security/decrypt")
async def decrypt_data(request: DecryptionRequest, current_user: dict = Depends(require_permission("write"))):
    """Decrypt encrypted data"""

    try:
        if not encryption_manager:
            raise HTTPException(status_code=503, detail="Encryption service not available")

        decrypted_data = encryption_manager.decrypt_data(request.encrypted_data)

        # Try to decode as UTF-8 string
        try:
            decoded_data = decrypted_data.decode('utf-8')
            # Try to parse as JSON
            try:
                import json
                decoded_data = json.loads(decoded_data)
            except:
                pass
        except:
            decoded_data = base64.b64encode(decrypted_data).decode('utf-8')

        return {
            "status": "success",
            "decrypted_data": decoded_data,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        osint_logger.log_error(e, "data_decryption")
        raise HTTPException(status_code=500, detail="Decryption failed")

@app.post("/api/v1/security/scan-sensitive")
async def scan_sensitive_data(text: str, current_user: dict = Depends(require_permission("read"))):
    """Scan text for sensitive information"""

    try:
        if not privacy_manager:
            raise HTTPException(status_code=503, detail="Privacy service not available")

        findings = privacy_manager.scan_for_sensitive_data(text)

        return {
            "status": "success",
            "sensitive_data_found": findings,
            "scan_timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        osint_logger.log_error(e, "sensitive_data_scan")
        raise HTTPException(status_code=500, detail="Sensitive data scan failed")

@app.post("/api/v1/security/sanitize")
async def sanitize_text(text: str, replacement: str = "[REDACTED]",
                       current_user: dict = Depends(require_permission("write"))):
    """Sanitize text by removing sensitive information"""

    try:
        if not privacy_manager:
            raise HTTPException(status_code=503, detail="Privacy service not available")

        sanitized_text = privacy_manager.sanitize_text(text, replacement)

        return {
            "status": "success",
            "original_length": len(text),
            "sanitized_text": sanitized_text,
            "sanitized_length": len(sanitized_text),
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        osint_logger.log_error(e, "text_sanitization")
        raise HTTPException(status_code=500, detail="Text sanitization failed")

@app.post("/api/v1/embeddings/generate")
async def generate_embeddings(texts: Union[str, List[str]],
                            current_user: dict = Depends(require_permission("read"))):
    """Generate local embeddings for texts"""

    try:
        if not local_embeddings or not local_embeddings.model:
            raise HTTPException(status_code=503, detail="Local embeddings service not available")

        embeddings = local_embeddings.generate_embeddings(texts)

        return {
            "status": "success",
            "embeddings": embeddings,
            "model": local_embeddings.model_name,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        osint_logger.log_error(e, "embeddings_generation")
        raise HTTPException(status_code=500, detail="Embeddings generation failed")

@app.post("/api/v1/embeddings/similarity")
async def compute_similarity(text1: str, text2: str,
                           current_user: dict = Depends(require_permission("read"))):
    """Compute similarity between two texts using local embeddings"""

    try:
        if not local_embeddings or not local_embeddings.model:
            raise HTTPException(status_code=503, detail="Local embeddings service not available")

        similarity = local_embeddings.compute_similarity(text1, text2)

        return {
            "status": "success",
            "similarity": similarity,
            "text1_length": len(text1),
            "text2_length": len(text2),
            "model": local_embeddings.model_name,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        osint_logger.log_error(e, "similarity_computation")
        raise HTTPException(status_code=500, detail="Similarity computation failed")

@app.get("/api/v1/security/capabilities")
async def get_security_capabilities():
    """Get available security and privacy capabilities"""

    capabilities = {
        "authentication": {
            "available": auth_manager is not None,
            "features": ["session_management", "user_creation", "permission_control"]
        },
        "encryption": {
            "available": encryption_manager is not None,
            "features": ["data_encryption", "file_encryption", "secure_storage"]
        },
        "privacy": {
            "available": privacy_manager is not None,
            "features": ["sensitive_data_detection", "data_sanitization", "anonymization"]
        },
        "local_embeddings": {
            "available": local_embeddings is not None and local_embeddings.model is not None,
            "model": local_embeddings.model_name if local_embeddings else None,
            "features": ["local_text_embeddings", "similarity_computation", "privacy_preserving"]
        },
        "permissions": [
            "read",
            "write",
            "admin"
        ],
        "security_features": {
            "secure_sessions": True,
            "data_encryption": encryption_manager is not None,
            "access_logging": True,
            "sensitive_data_protection": privacy_manager is not None,
            "local_processing": local_embeddings is not None
        }
    }

    return capabilities

# Workflow Management Endpoints

@app.post("/api/v1/workflows/execute")
async def execute_workflow(request: WorkflowExecutionRequest,
                          current_user: dict = Depends(require_permission("write"))):
    """Execute a workflow"""

    try:
        execution_id = await workflow_engine.execute_workflow(
            workflow_id=request.workflow_id,
            initial_context=request.initial_context
        )

        return {
            "status": "started",
            "execution_id": execution_id,
            "workflow_id": request.workflow_id,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        osint_logger.log_error(e, "workflow_execution")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/workflows")
async def list_workflows(current_user: dict = Depends(require_permission("read"))):
    """List all registered workflows"""

    try:
        workflows = workflow_engine.list_workflows()

        workflow_list = []
        for workflow in workflows:
            workflow_list.append({
                "id": workflow.id,
                "name": workflow.name,
                "description": workflow.description,
                "version": workflow.version,
                "task_count": len(workflow.tasks),
                "created_at": workflow.created_at.isoformat() if workflow.created_at else None,
                "max_concurrent_executions": workflow.max_concurrent_executions
            })

        return {
            "workflows": workflow_list,
            "total_count": len(workflow_list)
        }

    except Exception as e:
        osint_logger.log_error(e, "list_workflows")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/workflows/{workflow_id}")
async def get_workflow(workflow_id: str, current_user: dict = Depends(require_permission("read"))):
    """Get workflow details"""

    try:
        workflow_data = workflow_engine.export_workflow(workflow_id)

        if not workflow_data:
            raise HTTPException(status_code=404, detail="Workflow not found")

        return {
            "workflow": workflow_data,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        osint_logger.log_error(e, "get_workflow")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/workflows/{workflow_id}/executions")
async def list_workflow_executions(workflow_id: str,
                                  current_user: dict = Depends(require_permission("read"))):
    """List executions for a workflow"""

    try:
        executions = workflow_engine.list_executions(workflow_id)

        execution_list = []
        for execution in executions:
            execution_list.append({
                "execution_id": execution.execution_id,
                "workflow_id": execution.workflow_id,
                "status": execution.status.value,
                "created_at": execution.created_at.isoformat(),
                "started_at": execution.started_at.isoformat() if execution.started_at else None,
                "completed_at": execution.completed_at.isoformat() if execution.completed_at else None,
                "error_message": execution.error_message,
                "task_count": len(execution.task_results)
            })

        return {
            "executions": execution_list,
            "total_count": len(execution_list)
        }

    except Exception as e:
        osint_logger.log_error(e, "list_workflow_executions")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/workflows/executions/{execution_id}")
async def get_execution_status(execution_id: str,
                              current_user: dict = Depends(require_permission("read"))):
    """Get workflow execution status"""

    try:
        execution = workflow_engine.get_workflow_status(execution_id)

        if not execution:
            raise HTTPException(status_code=404, detail="Execution not found")

        return {
            "execution_id": execution.execution_id,
            "workflow_id": execution.workflow_id,
            "status": execution.status.value,
            "created_at": execution.created_at.isoformat(),
            "started_at": execution.started_at.isoformat() if execution.started_at else None,
            "completed_at": execution.completed_at.isoformat() if execution.completed_at else None,
            "error_message": execution.error_message,
            "task_results": execution.task_results,
            "context": execution.context
        }

    except HTTPException:
        raise
    except Exception as e:
        osint_logger.log_error(e, "get_execution_status")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/api/v1/workflows/executions/{execution_id}")
async def cancel_execution(execution_id: str,
                          current_user: dict = Depends(require_permission("write"))):
    """Cancel a running workflow execution"""

    try:
        success = await workflow_engine.cancel_execution(execution_id)

        if not success:
            raise HTTPException(status_code=404, detail="Execution not found or not running")

        return {
            "status": "cancelled",
            "execution_id": execution_id,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        osint_logger.log_error(e, "cancel_execution")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/workflows/schedule")
async def schedule_workflow(request: WorkflowScheduleRequest,
                           current_user: dict = Depends(require_permission("write"))):
    """Schedule a workflow"""

    try:
        # Convert string trigger type to enum
        trigger_type = TriggerType(request.trigger_type)

        schedule_id = workflow_scheduler.schedule_workflow(
            workflow_id=request.workflow_id,
            trigger_type=trigger_type,
            schedule=request.schedule,
            event_type=request.event_type,
            webhook_path=request.webhook_path,
            context=request.context,
            max_executions=request.max_executions
        )

        return {
            "status": "scheduled",
            "schedule_id": schedule_id,
            "workflow_id": request.workflow_id,
            "trigger_type": request.trigger_type,
            "timestamp": datetime.now().isoformat()
        }

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        osint_logger.log_error(e, "schedule_workflow")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/workflows/schedules")
async def list_scheduled_workflows(current_user: dict = Depends(require_permission("read"))):
    """List all scheduled workflows"""

    try:
        schedules = workflow_scheduler.get_scheduled_workflows()

        return {
            "schedules": schedules,
            "total_count": len(schedules),
            "scheduler_stats": workflow_scheduler.get_schedule_stats()
        }

    except Exception as e:
        osint_logger.log_error(e, "list_scheduled_workflows")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/api/v1/workflows/schedules/{schedule_id}")
async def unschedule_workflow(schedule_id: str,
                             current_user: dict = Depends(require_permission("write"))):
    """Unschedule a workflow"""

    try:
        success = workflow_scheduler.unschedule_workflow(schedule_id)

        if not success:
            raise HTTPException(status_code=404, detail="Schedule not found")

        return {
            "status": "unscheduled",
            "schedule_id": schedule_id,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        osint_logger.log_error(e, "unschedule_workflow")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/workflows/templates/create")
async def create_workflow_from_template(request: WorkflowTemplateRequest,
                                       current_user: dict = Depends(require_permission("write"))):
    """Create workflow from template"""

    try:
        template_name = request.template_name
        parameters = request.parameters

        # Create workflow from template
        if template_name == "comprehensive_investigation":
            target = parameters.get("target")
            if not target:
                raise HTTPException(status_code=400, detail="Target parameter required")

            builder = WorkflowTemplates.comprehensive_target_investigation(target)

        elif template_name == "threat_monitoring":
            builder = WorkflowTemplates.threat_monitoring_pipeline()

        elif template_name == "social_media_monitoring":
            target = parameters.get("target")
            platforms = parameters.get("platforms")
            if not target:
                raise HTTPException(status_code=400, detail="Target parameter required")

            builder = WorkflowTemplates.social_media_monitoring(target, platforms)

        elif template_name == "batch_domain_analysis":
            domains = parameters.get("domains")
            if not domains or not isinstance(domains, list):
                raise HTTPException(status_code=400, detail="Domains list parameter required")

            builder = WorkflowTemplates.batch_domain_analysis(domains)

        elif template_name == "incident_response":
            incident_type = parameters.get("incident_type")
            indicators = parameters.get("indicators")
            if not incident_type or not indicators:
                raise HTTPException(status_code=400, detail="incident_type and indicators parameters required")

            builder = WorkflowTemplates.incident_response_workflow(incident_type, indicators)

        else:
            raise HTTPException(status_code=400, detail=f"Unknown template: {template_name}")

        # Register the workflow
        workflow_id = builder.register()

        return {
            "status": "created",
            "workflow_id": workflow_id,
            "template_name": template_name,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        osint_logger.log_error(e, "create_workflow_from_template")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/workflows/templates")
async def list_workflow_templates():
    """List available workflow templates"""

    templates = [
        {
            "name": "comprehensive_investigation",
            "description": "Complete OSINT investigation of a target",
            "parameters": [
                {"name": "target", "type": "string", "required": True, "description": "Target to investigate"}
            ]
        },
        {
            "name": "threat_monitoring",
            "description": "Continuous threat monitoring pipeline",
            "parameters": []
        },
        {
            "name": "social_media_monitoring",
            "description": "Monitor social media presence for a target",
            "parameters": [
                {"name": "target", "type": "string", "required": True, "description": "Target to monitor"},
                {"name": "platforms", "type": "array", "required": False, "description": "Social media platforms to monitor"}
            ]
        },
        {
            "name": "batch_domain_analysis",
            "description": "Analyze multiple domains in parallel",
            "parameters": [
                {"name": "domains", "type": "array", "required": True, "description": "List of domains to analyze"}
            ]
        },
        {
            "name": "incident_response",
            "description": "Automated incident response workflow",
            "parameters": [
                {"name": "incident_type", "type": "string", "required": True, "description": "Type of incident"},
                {"name": "indicators", "type": "array", "required": True, "description": "List of indicators"}
            ]
        }
    ]

    return {
        "templates": templates,
        "total_count": len(templates)
    }

@app.post("/api/v1/workflows/events/{event_type}")
async def trigger_event(event_type: str, event_data: Dict[str, Any] = None,
                       current_user: dict = Depends(require_permission("write"))):
    """Trigger workflows based on event"""

    try:
        await workflow_scheduler.trigger_event(event_type, event_data)

        return {
            "status": "triggered",
            "event_type": event_type,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        osint_logger.log_error(e, "trigger_event")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/workflows/capabilities")
async def get_workflow_capabilities():
    """Get workflow system capabilities"""

    capabilities = {
        "workflow_engine": {
            "available": True,
            "features": [
                "multi_agent_orchestration",
                "dependency_management",
                "parallel_execution",
                "conditional_logic",
                "retry_mechanisms",
                "timeout_handling"
            ]
        },
        "scheduler": {
            "available": True,
            "trigger_types": [trigger.value for trigger in TriggerType],
            "features": [
                "cron_scheduling",
                "event_driven_execution",
                "webhook_triggers",
                "max_execution_limits"
            ]
        },
        "task_types": [task_type.value for task_type in TaskType],
        "agent_types": [
            "geopolitical",
            "cti",
            "multimodal",
            "browser"
        ],
        "tool_types": [
            "serper_search",
            "crawl_url",
            "multimodal_analysis"
        ],
        "templates": [
            "comprehensive_investigation",
            "threat_monitoring",
            "social_media_monitoring",
            "batch_domain_analysis",
            "incident_response"
        ],
        "features": {
            "visual_workflow_builder": False,  # Future feature
            "workflow_versioning": False,      # Future feature
            "workflow_templates": True,
            "event_driven_execution": True,
            "batch_processing": True,
            "parallel_execution": True,
            "conditional_logic": True,
            "webhook_integration": True,
            "scheduling": True
        }
    }

    return capabilities

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
