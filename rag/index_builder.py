"""
Vector index builder for document ingestion and indexing.

This module provides comprehensive document indexing capabilities using LlamaIndex
with support for multiple document sources, persistent storage, and advanced
text processing. It includes optimizations for performance, memory usage, and
robust error handling.
"""

import os
import logging
import asyncio
import hashlib
import json
from datetime import datetime
from typing import List, Dict, Any, Optional, Union, Tuple
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed

from llama_index.core import (
    VectorStoreIndex,
    Document,
    StorageContext,
    load_index_from_storage,
    Settings
)
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.llms.openai import OpenAI
from llama_index.core.node_parser import SentenceSplitter
from llama_index.core.extractors import TitleExtractor, QuestionsAnsweredExtractor
from llama_index.core.schema import BaseNode

try:
    import requests
    from bs4 import BeautifulSoup
    WEB_SCRAPING_AVAILABLE = True
except ImportError:
    WEB_SCRAPING_AVAILABLE = False
    requests = None
    BeautifulSoup = None


class IndexBuilder:
    """
    Advanced vector index builder for document retrieval with optimization features.

    This class provides comprehensive document indexing capabilities with support for:
    - Multiple document sources (files, URLs, text strings)
    - Persistent storage with metadata tracking
    - Incremental updates and deduplication
    - Performance optimization with caching and parallel processing
    - Advanced text processing and extraction
    - Index management and statistics

    Attributes:
        storage_dir: Directory for persistent index storage
        embedding_model: OpenAI embedding model name
        llm_model: Language model for processing
        chunk_size: Text chunk size for indexing
        chunk_overlap: Overlap between text chunks
        max_workers: Maximum worker threads for parallel processing
    """

    def __init__(
        self,
        storage_dir: str = "./data/vector_index",
        embedding_model: str = "text-embedding-3-small",  # Updated to newer model
        llm_model: str = "gpt-3.5-turbo",
        chunk_size: int = 1024,
        chunk_overlap: int = 200,
        max_workers: int = 4,
        enable_caching: bool = True
    ) -> None:
        """
        Initialize the index builder with advanced configuration.

        Args:
            storage_dir: Directory to store vector indexes
            embedding_model: OpenAI embedding model (default: text-embedding-3-small)
            llm_model: LLM model for text processing
            chunk_size: Size of text chunks for indexing (512-2048 recommended)
            chunk_overlap: Overlap between chunks (10-20% of chunk_size)
            max_workers: Maximum threads for parallel processing
            enable_caching: Enable document content caching for deduplication

        Raises:
            ValueError: If configuration parameters are invalid
            OSError: If storage directory cannot be created
        """
        # Validate parameters
        self._validate_init_params(chunk_size, chunk_overlap, max_workers)

        # Initialize storage
        self.storage_dir = Path(storage_dir)
        self.storage_dir.mkdir(parents=True, exist_ok=True)

        # Store configuration
        self.embedding_model = embedding_model
        self.llm_model = llm_model
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.max_workers = max_workers
        self.enable_caching = enable_caching

        # Configure LlamaIndex settings
        self._configure_llamaindex()

        # Initialize text processing components
        self._initialize_processors()

        # Initialize caching if enabled
        self._cache_dir = self.storage_dir / "cache" if enable_caching else None
        if self._cache_dir:
            self._cache_dir.mkdir(exist_ok=True)

        # Performance tracking
        self._stats = {
            "documents_processed": 0,
            "total_processing_time": 0.0,
            "cache_hits": 0,
            "cache_misses": 0
        }

        self.logger = logging.getLogger(__name__)
        self.logger.info(f"IndexBuilder initialized with {embedding_model} embeddings")

    def _validate_init_params(self, chunk_size: int, chunk_overlap: int, max_workers: int) -> None:
        """Validate initialization parameters."""
        if chunk_size < 100 or chunk_size > 4096:
            raise ValueError("chunk_size must be between 100 and 4096")

        if chunk_overlap < 0 or chunk_overlap >= chunk_size:
            raise ValueError("chunk_overlap must be between 0 and chunk_size")

        if max_workers < 1 or max_workers > 16:
            raise ValueError("max_workers must be between 1 and 16")

    def _configure_llamaindex(self) -> None:
        """Configure LlamaIndex global settings."""
        Settings.llm = OpenAI(model=self.llm_model, temperature=0.1)
        Settings.embed_model = OpenAIEmbedding(model=self.embedding_model)
        Settings.chunk_size = self.chunk_size
        Settings.chunk_overlap = self.chunk_overlap

    def _initialize_processors(self) -> None:
        """Initialize text processing components."""
        # Initialize text splitter with optimized settings
        self.text_splitter = SentenceSplitter(
            chunk_size=self.chunk_size,
            chunk_overlap=self.chunk_overlap,
            separator=" ",  # Use space as primary separator
            backup_separators=["\n", "\t"],  # Fallback separators
        )

        # Initialize metadata extractors
        self.extractors = [
            TitleExtractor(nodes=5, llm=Settings.llm),
            QuestionsAnsweredExtractor(questions=3, llm=Settings.llm)
        ]
    
    def build_index(
        self,
        documents: Optional[List[Document]] = None,
        urls: Optional[List[str]] = None,
        file_paths: Optional[List[str]] = None,
        texts: Optional[List[str]] = None,
        metadata_list: Optional[List[Dict[str, Any]]] = None,
        index_name: Optional[str] = None,
        enable_deduplication: bool = True,
        batch_size: int = 100
    ) -> VectorStoreIndex:
        """
        Build a vector index from various document sources with optimization.

        This method processes documents from multiple sources, applies deduplication,
        and builds an optimized vector index with comprehensive error handling.

        Args:
            documents: Pre-created LlamaIndex Document objects
            urls: URLs to crawl and index (requires web scraping dependencies)
            file_paths: File paths to read and index
            texts: Raw text strings to index
            metadata_list: Optional metadata for each text (must match texts length)
            index_name: Optional name for saving the index
            enable_deduplication: Remove duplicate content based on hash
            batch_size: Process documents in batches for memory efficiency

        Returns:
            VectorStoreIndex instance ready for querying

        Raises:
            ValueError: If no documents provided or invalid parameters
            RuntimeError: If index building fails
        """
        start_time = datetime.now()

        try:
            # Collect all documents from various sources
            all_documents = self._collect_documents(
                documents, urls, file_paths, texts, metadata_list
            )

            if not all_documents:
                raise ValueError("No valid documents provided for indexing")

            # Apply deduplication if enabled
            if enable_deduplication:
                all_documents = self._deduplicate_documents(all_documents)
                self.logger.info(f"After deduplication: {len(all_documents)} documents")

            # Build index with batching for memory efficiency
            index = self._build_index_batched(all_documents, batch_size)

            # Save index if name provided
            if index_name:
                self.save_index(index, index_name)

            # Update statistics
            processing_time = (datetime.now() - start_time).total_seconds()
            self._stats["documents_processed"] += len(all_documents)
            self._stats["total_processing_time"] += processing_time

            self.logger.info(
                f"Index built successfully with {len(all_documents)} documents "
                f"in {processing_time:.2f} seconds"
            )

            return index

        except Exception as e:
            self.logger.error(f"Failed to build index: {str(e)}")
            raise RuntimeError(f"Index building failed: {str(e)}") from e

    def _collect_documents(
        self,
        documents: Optional[List[Document]],
        urls: Optional[List[str]],
        file_paths: Optional[List[str]],
        texts: Optional[List[str]],
        metadata_list: Optional[List[Dict[str, Any]]]
    ) -> List[Document]:
        """Collect documents from all provided sources."""
        all_documents = []

        # Add provided documents
        if documents:
            all_documents.extend(documents)
            self.logger.info(f"Added {len(documents)} pre-created documents")

        # Process URLs with parallel processing
        if urls:
            if WEB_SCRAPING_AVAILABLE:
                url_docs = self._process_urls_parallel(urls)
                all_documents.extend(url_docs)
                self.logger.info(f"Processed {len(url_docs)} URLs")
            else:
                self.logger.warning(
                    "Web scraping not available. Install requests and beautifulsoup4 "
                    "to process URLs."
                )

        # Process file paths with parallel processing
        if file_paths:
            file_docs = self._process_files_parallel(file_paths)
            all_documents.extend(file_docs)
            self.logger.info(f"Processed {len(file_docs)} files")

        # Process text strings
        if texts:
            if metadata_list and len(metadata_list) != len(texts):
                raise ValueError("metadata_list length must match texts length")

            text_docs = self._process_texts(texts, metadata_list)
            all_documents.extend(text_docs)
            self.logger.info(f"Processed {len(text_docs)} text strings")

        return all_documents

    def _deduplicate_documents(self, documents: List[Document]) -> List[Document]:
        """Remove duplicate documents based on content hash."""
        if not self.enable_caching:
            return documents

        seen_hashes = set()
        unique_documents = []

        for doc in documents:
            content_hash = self._get_content_hash(doc.text)

            if content_hash not in seen_hashes:
                seen_hashes.add(content_hash)
                unique_documents.append(doc)
                # Update metadata with hash for tracking
                doc.metadata["content_hash"] = content_hash
            else:
                self.logger.debug(f"Skipping duplicate document: {doc.metadata.get('source', 'unknown')}")

        duplicates_removed = len(documents) - len(unique_documents)
        if duplicates_removed > 0:
            self.logger.info(f"Removed {duplicates_removed} duplicate documents")

        return unique_documents

    def _get_content_hash(self, text: str) -> str:
        """Generate hash for document content."""
        # Normalize text for consistent hashing
        normalized = " ".join(text.split()).lower()
        return hashlib.md5(normalized.encode()).hexdigest()

    def _build_index_batched(self, documents: List[Document], batch_size: int) -> VectorStoreIndex:
        """Build index with batching for memory efficiency."""
        if len(documents) <= batch_size:
            # Small dataset, build directly
            return VectorStoreIndex.from_documents(
                documents,
                transformations=[self.text_splitter] + self.extractors,
                show_progress=True
            )

        # Large dataset, build incrementally
        self.logger.info(f"Building index incrementally with batch size {batch_size}")

        # Build initial index with first batch
        first_batch = documents[:batch_size]
        index = VectorStoreIndex.from_documents(
            first_batch,
            transformations=[self.text_splitter] + self.extractors,
            show_progress=True
        )

        # Add remaining documents in batches
        for i in range(batch_size, len(documents), batch_size):
            batch = documents[i:i + batch_size]
            self.logger.info(f"Processing batch {i//batch_size + 1}/{(len(documents)-1)//batch_size + 1}")

            for doc in batch:
                index.insert(doc)

        return index
    
    def _process_urls_parallel(self, urls: List[str]) -> List[Document]:
        """
        Process URLs in parallel with enhanced error handling and caching.

        Args:
            urls: List of URLs to process

        Returns:
            List of successfully processed documents
        """
        if not WEB_SCRAPING_AVAILABLE:
            self.logger.error("Web scraping dependencies not available")
            return []

        documents = []

        # Use ThreadPoolExecutor for parallel processing
        with ThreadPoolExecutor(max_workers=min(self.max_workers, len(urls))) as executor:
            # Submit all URL processing tasks
            future_to_url = {
                executor.submit(self._process_single_url, url): url
                for url in urls
            }

            # Collect results as they complete
            for future in as_completed(future_to_url):
                url = future_to_url[future]
                try:
                    doc = future.result(timeout=60)  # 60 second timeout per URL
                    if doc:
                        documents.append(doc)
                        self.logger.info(f"Successfully processed URL: {url}")
                except Exception as e:
                    self.logger.error(f"Failed to process URL {url}: {str(e)}")

        return documents

    def _process_single_url(self, url: str) -> Optional[Document]:
        """
        Process a single URL with caching and robust error handling.

        Args:
            url: URL to process

        Returns:
            Document object or None if processing fails
        """
        try:
            # Check cache first
            if self.enable_caching:
                cached_doc = self._get_cached_url(url)
                if cached_doc:
                    self._stats["cache_hits"] += 1
                    return cached_doc
                self._stats["cache_misses"] += 1

            # Configure request with proper headers
            headers = {
                'User-Agent': 'Mozilla/5.0 (compatible; OSINT-Agent/1.0)',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
            }

            # Fetch content with timeout and retries
            response = requests.get(
                url,
                headers=headers,
                timeout=30,
                allow_redirects=True,
                verify=True
            )
            response.raise_for_status()

            # Check content type
            content_type = response.headers.get('content-type', '').lower()
            if 'text/html' not in content_type and 'text/plain' not in content_type:
                self.logger.warning(f"Unsupported content type for {url}: {content_type}")
                return None

            # Parse HTML content
            soup = BeautifulSoup(response.content, 'html.parser')

            # Extract and clean text content
            text = self._extract_clean_text(soup)

            if len(text.strip()) < 100:  # Skip very short content
                self.logger.warning(f"Content too short for {url}: {len(text)} characters")
                return None

            # Extract metadata
            metadata = self._extract_url_metadata(url, soup, response, text)

            # Create document
            doc = Document(text=text, metadata=metadata)

            # Cache the document
            if self.enable_caching:
                self._cache_url_document(url, doc)

            return doc

        except requests.exceptions.Timeout:
            self.logger.error(f"Timeout processing URL: {url}")
        except requests.exceptions.HTTPError as e:
            self.logger.error(f"HTTP error for {url}: {e.response.status_code}")
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Request error for {url}: {str(e)}")
        except Exception as e:
            self.logger.error(f"Unexpected error processing {url}: {str(e)}")

        return None

    def _extract_clean_text(self, soup: BeautifulSoup) -> str:
        """Extract and clean text content from BeautifulSoup object."""
        # Remove unwanted elements
        for element in soup(["script", "style", "nav", "header", "footer", "aside", "noscript"]):
            element.decompose()

        # Get text content
        text = soup.get_text()

        # Clean up whitespace and formatting
        lines = (line.strip() for line in text.splitlines())
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
        cleaned_text = ' '.join(chunk for chunk in chunks if chunk)

        return cleaned_text

    def _extract_url_metadata(
        self,
        url: str,
        soup: BeautifulSoup,
        response: requests.Response,
        text: str
    ) -> Dict[str, Any]:
        """Extract comprehensive metadata from URL response."""
        metadata = {
            "source": url,
            "source_type": "url",
            "content_length": len(text),
            "word_count": len(text.split()),
            "processed_at": datetime.now().isoformat(),
            "status_code": response.status_code,
            "content_type": response.headers.get('content-type', ''),
        }

        # Extract title
        if soup.title:
            metadata["title"] = soup.title.string.strip() if soup.title.string else ""

        # Extract meta description
        meta_desc = soup.find("meta", attrs={"name": "description"})
        if meta_desc and meta_desc.get("content"):
            metadata["description"] = meta_desc["content"].strip()

        # Extract meta keywords
        meta_keywords = soup.find("meta", attrs={"name": "keywords"})
        if meta_keywords and meta_keywords.get("content"):
            metadata["keywords"] = meta_keywords["content"].strip()

        # Extract language
        html_tag = soup.find("html")
        if html_tag and html_tag.get("lang"):
            metadata["language"] = html_tag["lang"]

        # Extract author if available
        meta_author = soup.find("meta", attrs={"name": "author"})
        if meta_author and meta_author.get("content"):
            metadata["author"] = meta_author["content"].strip()

        return metadata
    
    def _process_files_parallel(self, file_paths: List[str]) -> List[Document]:
        """
        Process files in parallel with enhanced error handling and format support.

        Args:
            file_paths: List of file paths to process

        Returns:
            List of successfully processed documents
        """
        documents = []

        # Use ThreadPoolExecutor for parallel file processing
        with ThreadPoolExecutor(max_workers=min(self.max_workers, len(file_paths))) as executor:
            # Submit all file processing tasks
            future_to_path = {
                executor.submit(self._process_single_file, file_path): file_path
                for file_path in file_paths
            }

            # Collect results as they complete
            for future in as_completed(future_to_path):
                file_path = future_to_path[future]
                try:
                    doc = future.result()
                    if doc:
                        documents.append(doc)
                        self.logger.info(f"Successfully processed file: {file_path}")
                except Exception as e:
                    self.logger.error(f"Failed to process file {file_path}: {str(e)}")

        return documents

    def _process_single_file(self, file_path: str) -> Optional[Document]:
        """
        Process a single file with format detection and caching.

        Args:
            file_path: Path to the file to process

        Returns:
            Document object or None if processing fails
        """
        try:
            path = Path(file_path)

            # Check if file exists and is readable
            if not path.exists():
                self.logger.warning(f"File not found: {file_path}")
                return None

            if not path.is_file():
                self.logger.warning(f"Path is not a file: {file_path}")
                return None

            # Check cache first
            if self.enable_caching:
                cached_doc = self._get_cached_file(path)
                if cached_doc:
                    self._stats["cache_hits"] += 1
                    return cached_doc
                self._stats["cache_misses"] += 1

            # Get file stats
            file_stat = path.stat()
            file_size = file_stat.st_size

            # Skip very large files (>10MB by default)
            max_file_size = 10 * 1024 * 1024  # 10MB
            if file_size > max_file_size:
                self.logger.warning(f"File too large ({file_size} bytes): {file_path}")
                return None

            # Detect file type and extract content
            text = self._extract_file_content(path)

            if not text or len(text.strip()) < 10:
                self.logger.warning(f"No content extracted from file: {file_path}")
                return None

            # Create metadata
            metadata = self._create_file_metadata(path, text, file_stat)

            # Create document
            doc = Document(text=text, metadata=metadata)

            # Cache the document
            if self.enable_caching:
                self._cache_file_document(path, doc)

            return doc

        except Exception as e:
            self.logger.error(f"Error processing file {file_path}: {str(e)}")
            return None

    def _extract_file_content(self, path: Path) -> str:
        """
        Extract content from file based on its type.

        Args:
            path: Path to the file

        Returns:
            Extracted text content
        """
        file_extension = path.suffix.lower()

        try:
            if file_extension in ['.txt', '.md', '.rst', '.log']:
                # Plain text files
                return self._read_text_file(path)
            elif file_extension in ['.json']:
                # JSON files - extract text values
                return self._extract_json_content(path)
            elif file_extension in ['.csv']:
                # CSV files - convert to text
                return self._extract_csv_content(path)
            elif file_extension in ['.html', '.htm']:
                # HTML files - extract text content
                return self._extract_html_content(path)
            else:
                # Try to read as text file
                self.logger.warning(f"Unknown file type {file_extension}, trying as text: {path}")
                return self._read_text_file(path)

        except Exception as e:
            self.logger.error(f"Failed to extract content from {path}: {str(e)}")
            return ""

    def _read_text_file(self, path: Path) -> str:
        """Read a plain text file with encoding detection."""
        encodings = ['utf-8', 'utf-16', 'latin-1', 'cp1252']

        for encoding in encodings:
            try:
                with open(path, 'r', encoding=encoding) as f:
                    return f.read()
            except UnicodeDecodeError:
                continue

        # If all encodings fail, read as binary and decode with errors='ignore'
        with open(path, 'rb') as f:
            return f.read().decode('utf-8', errors='ignore')

    def _extract_json_content(self, path: Path) -> str:
        """Extract text content from JSON file."""
        try:
            with open(path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Extract all string values from JSON
            text_parts = []
            self._extract_json_strings(data, text_parts)

            return ' '.join(text_parts)
        except Exception as e:
            self.logger.error(f"Failed to parse JSON file {path}: {str(e)}")
            return ""

    def _extract_json_strings(self, obj: Any, text_parts: List[str]) -> None:
        """Recursively extract string values from JSON object."""
        if isinstance(obj, str):
            if len(obj.strip()) > 0:
                text_parts.append(obj.strip())
        elif isinstance(obj, dict):
            for value in obj.values():
                self._extract_json_strings(value, text_parts)
        elif isinstance(obj, list):
            for item in obj:
                self._extract_json_strings(item, text_parts)

    def _extract_csv_content(self, path: Path) -> str:
        """Extract content from CSV file."""
        try:
            import csv
            text_parts = []

            with open(path, 'r', encoding='utf-8') as f:
                reader = csv.reader(f)
                for row in reader:
                    # Join row values with spaces
                    row_text = ' '.join(str(cell).strip() for cell in row if str(cell).strip())
                    if row_text:
                        text_parts.append(row_text)

            return '\n'.join(text_parts)
        except Exception as e:
            self.logger.error(f"Failed to parse CSV file {path}: {str(e)}")
            return ""

    def _extract_html_content(self, path: Path) -> str:
        """Extract text content from HTML file."""
        try:
            if not BeautifulSoup:
                # Fallback to reading as text if BeautifulSoup not available
                return self._read_text_file(path)

            with open(path, 'r', encoding='utf-8') as f:
                content = f.read()

            soup = BeautifulSoup(content, 'html.parser')
            return self._extract_clean_text(soup)

        except Exception as e:
            self.logger.error(f"Failed to parse HTML file {path}: {str(e)}")
            return ""

    def _create_file_metadata(self, path: Path, text: str, file_stat: os.stat_result) -> Dict[str, Any]:
        """Create comprehensive metadata for a file."""
        return {
            "source": str(path.absolute()),
            "source_type": "file",
            "filename": path.name,
            "file_extension": path.suffix.lower(),
            "file_size": file_stat.st_size,
            "content_length": len(text),
            "word_count": len(text.split()),
            "created_at": datetime.fromtimestamp(file_stat.st_ctime).isoformat(),
            "modified_at": datetime.fromtimestamp(file_stat.st_mtime).isoformat(),
            "processed_at": datetime.now().isoformat()
        }
    
    def _process_texts(
        self,
        texts: List[str],
        metadata_list: Optional[List[Dict[str, Any]]] = None
    ) -> List[Document]:
        """
        Process text strings into documents with enhanced metadata.

        Args:
            texts: List of text strings to process
            metadata_list: Optional metadata for each text

        Returns:
            List of Document objects
        """
        documents = []

        for i, text in enumerate(texts):
            if not text or len(text.strip()) < 10:
                self.logger.warning(f"Skipping empty or very short text at index {i}")
                continue

            # Create base metadata
            metadata = {
                "source": f"text_{i}",
                "source_type": "text",
                "content_length": len(text),
                "word_count": len(text.split()),
                "processed_at": datetime.now().isoformat(),
                "text_index": i
            }

            # Add custom metadata if provided
            if metadata_list and i < len(metadata_list):
                metadata.update(metadata_list[i])

            doc = Document(text=text.strip(), metadata=metadata)
            documents.append(doc)

        return documents

    # Caching methods
    def _get_cached_url(self, url: str) -> Optional[Document]:
        """Get cached document for URL if available and fresh."""
        if not self._cache_dir:
            return None

        cache_key = hashlib.md5(url.encode()).hexdigest()
        cache_file = self._cache_dir / f"url_{cache_key}.json"

        if not cache_file.exists():
            return None

        try:
            # Check if cache is fresh (24 hours)
            cache_age = datetime.now().timestamp() - cache_file.stat().st_mtime
            if cache_age > 24 * 3600:  # 24 hours
                cache_file.unlink()  # Remove stale cache
                return None

            with open(cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)

            return Document(text=cache_data['text'], metadata=cache_data['metadata'])

        except Exception as e:
            self.logger.warning(f"Failed to load cache for URL {url}: {str(e)}")
            return None

    def _cache_url_document(self, url: str, doc: Document) -> None:
        """Cache document for URL."""
        if not self._cache_dir:
            return

        try:
            cache_key = hashlib.md5(url.encode()).hexdigest()
            cache_file = self._cache_dir / f"url_{cache_key}.json"

            cache_data = {
                'text': doc.text,
                'metadata': doc.metadata,
                'cached_at': datetime.now().isoformat()
            }

            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.logger.warning(f"Failed to cache URL {url}: {str(e)}")

    def _get_cached_file(self, path: Path) -> Optional[Document]:
        """Get cached document for file if available and fresh."""
        if not self._cache_dir:
            return None

        cache_key = hashlib.md5(str(path.absolute()).encode()).hexdigest()
        cache_file = self._cache_dir / f"file_{cache_key}.json"

        if not cache_file.exists():
            return None

        try:
            # Check if file has been modified since caching
            file_mtime = path.stat().st_mtime
            cache_mtime = cache_file.stat().st_mtime

            if file_mtime > cache_mtime:
                cache_file.unlink()  # Remove stale cache
                return None

            with open(cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)

            return Document(text=cache_data['text'], metadata=cache_data['metadata'])

        except Exception as e:
            self.logger.warning(f"Failed to load cache for file {path}: {str(e)}")
            return None

    def _cache_file_document(self, path: Path, doc: Document) -> None:
        """Cache document for file."""
        if not self._cache_dir:
            return

        try:
            cache_key = hashlib.md5(str(path.absolute()).encode()).hexdigest()
            cache_file = self._cache_dir / f"file_{cache_key}.json"

            cache_data = {
                'text': doc.text,
                'metadata': doc.metadata,
                'cached_at': datetime.now().isoformat()
            }

            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.logger.warning(f"Failed to cache file {path}: {str(e)}")

    def clear_cache(self) -> bool:
        """
        Clear all cached documents.

        Returns:
            True if cache cleared successfully, False otherwise
        """
        if not self._cache_dir or not self._cache_dir.exists():
            return True

        try:
            import shutil
            shutil.rmtree(self._cache_dir)
            self._cache_dir.mkdir(exist_ok=True)
            self.logger.info("Document cache cleared successfully")
            return True
        except Exception as e:
            self.logger.error(f"Failed to clear cache: {str(e)}")
            return False

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        if not self._cache_dir or not self._cache_dir.exists():
            return {"cache_enabled": False}

        try:
            cache_files = list(self._cache_dir.glob("*.json"))
            total_size = sum(f.stat().st_size for f in cache_files)

            return {
                "cache_enabled": True,
                "cache_dir": str(self._cache_dir),
                "cached_items": len(cache_files),
                "total_size_bytes": total_size,
                "cache_hits": self._stats["cache_hits"],
                "cache_misses": self._stats["cache_misses"]
            }
        except Exception as e:
            return {"cache_enabled": True, "error": str(e)}
    
    def save_index(self, index: VectorStoreIndex, index_name: str = "default") -> str:
        """
        Save an index to persistent storage with metadata.

        Args:
            index: VectorStoreIndex to save
            index_name: Name for the saved index

        Returns:
            Path to saved index

        Raises:
            RuntimeError: If saving fails
        """
        try:
            index_path = self.storage_dir / index_name
            index_path.mkdir(exist_ok=True)

            # Persist the index
            index.storage_context.persist(persist_dir=str(index_path))

            # Save metadata about the index
            metadata = {
                "index_name": index_name,
                "created_at": datetime.now().isoformat(),
                "embedding_model": self.embedding_model,
                "llm_model": self.llm_model,
                "chunk_size": self.chunk_size,
                "chunk_overlap": self.chunk_overlap,
                "num_documents": len(index.docstore.docs) if hasattr(index, 'docstore') else 0,
                "builder_stats": self._stats.copy()
            }

            metadata_file = index_path / "index_metadata.json"
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)

            self.logger.info(f"Index '{index_name}' saved to: {index_path}")
            return str(index_path)

        except Exception as e:
            error_msg = f"Failed to save index '{index_name}': {str(e)}"
            self.logger.error(error_msg)
            raise RuntimeError(error_msg) from e
    
    def load_index(self, index_name: str = "default") -> Optional[VectorStoreIndex]:
        """
        Load an index from persistent storage with validation.

        Args:
            index_name: Name of the index to load

        Returns:
            VectorStoreIndex instance or None if not found/invalid
        """
        index_path = self.storage_dir / index_name

        if not index_path.exists():
            self.logger.warning(f"Index directory not found: {index_path}")
            return None

        # Check for required index files
        required_files = ["docstore.json", "index_store.json", "vector_store.json"]
        missing_files = [f for f in required_files if not (index_path / f).exists()]

        if missing_files:
            self.logger.error(f"Index incomplete, missing files: {missing_files}")
            return None

        try:
            # Load metadata if available
            metadata_file = index_path / "index_metadata.json"
            if metadata_file.exists():
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
                self.logger.info(f"Loading index created at {metadata.get('created_at', 'unknown')}")

            # Load the storage context
            storage_context = StorageContext.from_defaults(persist_dir=str(index_path))

            # Load the index
            index = load_index_from_storage(storage_context)

            # Validate the loaded index
            if not hasattr(index, 'docstore') or not index.docstore.docs:
                self.logger.warning(f"Loaded index appears to be empty: {index_name}")

            self.logger.info(f"Index '{index_name}' loaded successfully from: {index_path}")
            return index

        except Exception as e:
            self.logger.error(f"Error loading index '{index_name}': {str(e)}")
            return None
    
    def list_indexes(self) -> List[str]:
        """List available saved indexes."""
        if not self.storage_dir.exists():
            return []
        
        indexes = []
        for item in self.storage_dir.iterdir():
            if item.is_dir() and (item / "docstore.json").exists():
                indexes.append(item.name)
        
        return indexes
    
    def delete_index(self, index_name: str) -> bool:
        """
        Delete a saved index.
        
        Args:
            index_name: Name of the index to delete
            
        Returns:
            True if deleted successfully, False otherwise
        """
        index_path = self.storage_dir / index_name
        
        if not index_path.exists():
            self.logger.warning(f"Index not found: {index_path}")
            return False
        
        try:
            import shutil
            shutil.rmtree(index_path)
            self.logger.info(f"Index deleted: {index_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error deleting index: {str(e)}")
            return False
    
    def get_index_info(self, index_name: str = "default") -> Dict[str, Any]:
        """
        Get comprehensive information about a saved index.

        Args:
            index_name: Name of the index

        Returns:
            Dictionary with detailed index information
        """
        index_path = self.storage_dir / index_name

        if not index_path.exists():
            return {"error": "Index not found", "index_name": index_name}

        try:
            info = {
                "name": index_name,
                "path": str(index_path),
                "exists": True
            }

            # Get directory statistics
            index_files = list(index_path.rglob('*'))
            file_sizes = [f.stat().st_size for f in index_files if f.is_file()]

            info.update({
                "total_files": len([f for f in index_files if f.is_file()]),
                "total_size_bytes": sum(file_sizes),
                "total_size_mb": round(sum(file_sizes) / (1024 * 1024), 2),
                "created_at": datetime.fromtimestamp(index_path.stat().st_ctime).isoformat(),
                "modified_at": datetime.fromtimestamp(index_path.stat().st_mtime).isoformat()
            })

            # Load metadata if available
            metadata_file = index_path / "index_metadata.json"
            if metadata_file.exists():
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
                info["metadata"] = metadata

            # Try to load index for detailed statistics
            try:
                index = self.load_index(index_name)
                if index and hasattr(index, 'docstore'):
                    docs = index.docstore.docs
                    info.update({
                        "num_documents": len(docs),
                        "document_sources": self._analyze_document_sources(docs),
                        "content_statistics": self._calculate_content_stats(docs)
                    })
            except Exception as e:
                info["load_error"] = str(e)

            return info

        except Exception as e:
            return {"error": f"Error getting index info: {str(e)}", "index_name": index_name}

    def _analyze_document_sources(self, docs: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze document sources in the index."""
        source_types = {}
        sources = []

        for doc_id, doc in docs.items():
            if hasattr(doc, 'metadata') and doc.metadata:
                source_type = doc.metadata.get('source_type', 'unknown')
                source = doc.metadata.get('source', 'unknown')

                source_types[source_type] = source_types.get(source_type, 0) + 1
                sources.append(source)

        return {
            "source_types": source_types,
            "unique_sources": len(set(sources)),
            "total_documents": len(docs)
        }

    def _calculate_content_stats(self, docs: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate content statistics for documents."""
        total_length = 0
        total_words = 0
        content_lengths = []

        for doc_id, doc in docs.items():
            if hasattr(doc, 'text') and doc.text:
                length = len(doc.text)
                words = len(doc.text.split())

                total_length += length
                total_words += words
                content_lengths.append(length)

        if not content_lengths:
            return {"error": "No content found"}

        return {
            "total_characters": total_length,
            "total_words": total_words,
            "average_document_length": round(total_length / len(content_lengths), 2),
            "average_words_per_document": round(total_words / len(content_lengths), 2),
            "min_document_length": min(content_lengths),
            "max_document_length": max(content_lengths)
        }

    def get_performance_stats(self) -> Dict[str, Any]:
        """
        Get performance statistics for the IndexBuilder.

        Returns:
            Dictionary with performance metrics
        """
        stats = self._stats.copy()

        # Calculate derived metrics
        if stats["documents_processed"] > 0:
            stats["average_processing_time"] = round(
                stats["total_processing_time"] / stats["documents_processed"], 3
            )

        if stats["cache_hits"] + stats["cache_misses"] > 0:
            stats["cache_hit_rate"] = round(
                stats["cache_hits"] / (stats["cache_hits"] + stats["cache_misses"]), 3
            )

        # Add configuration info
        stats["configuration"] = {
            "embedding_model": self.embedding_model,
            "llm_model": self.llm_model,
            "chunk_size": self.chunk_size,
            "chunk_overlap": self.chunk_overlap,
            "max_workers": self.max_workers,
            "caching_enabled": self.enable_caching
        }

        return stats

    def reset_stats(self) -> None:
        """Reset performance statistics."""
        self._stats = {
            "documents_processed": 0,
            "total_processing_time": 0.0,
            "cache_hits": 0,
            "cache_misses": 0
        }
        self.logger.info("Performance statistics reset")
    
    def update_index(
        self,
        index: VectorStoreIndex,
        new_documents: List[Document],
        enable_deduplication: bool = True,
        batch_size: int = 50
    ) -> VectorStoreIndex:
        """
        Update an existing index with new documents efficiently.

        Args:
            index: Existing VectorStoreIndex to update
            new_documents: New documents to add
            enable_deduplication: Remove duplicates before adding
            batch_size: Process documents in batches

        Returns:
            Updated VectorStoreIndex

        Raises:
            ValueError: If no new documents provided
            RuntimeError: If update fails
        """
        if not new_documents:
            raise ValueError("No new documents provided for update")

        start_time = datetime.now()

        try:
            self.logger.info(f"Updating index with {len(new_documents)} new documents")

            # Apply deduplication if enabled
            documents_to_add = new_documents
            if enable_deduplication:
                documents_to_add = self._deduplicate_against_index(index, new_documents)
                self.logger.info(f"After deduplication: {len(documents_to_add)} documents to add")

            if not documents_to_add:
                self.logger.info("No new unique documents to add")
                return index

            # Add documents in batches for better performance
            added_count = 0
            for i in range(0, len(documents_to_add), batch_size):
                batch = documents_to_add[i:i + batch_size]

                for doc in batch:
                    index.insert(doc)
                    added_count += 1

                if len(documents_to_add) > batch_size:
                    self.logger.info(f"Added batch {i//batch_size + 1}, total: {added_count} documents")

            # Update statistics
            processing_time = (datetime.now() - start_time).total_seconds()
            self._stats["documents_processed"] += added_count
            self._stats["total_processing_time"] += processing_time

            self.logger.info(
                f"Index updated successfully with {added_count} new documents "
                f"in {processing_time:.2f} seconds"
            )

            return index

        except Exception as e:
            error_msg = f"Failed to update index: {str(e)}"
            self.logger.error(error_msg)
            raise RuntimeError(error_msg) from e

    def _deduplicate_against_index(
        self,
        index: VectorStoreIndex,
        new_documents: List[Document]
    ) -> List[Document]:
        """Remove documents that already exist in the index."""
        if not hasattr(index, 'docstore') or not index.docstore.docs:
            return new_documents

        # Get existing document hashes
        existing_hashes = set()
        for doc_id, doc in index.docstore.docs.items():
            if hasattr(doc, 'metadata') and 'content_hash' in doc.metadata:
                existing_hashes.add(doc.metadata['content_hash'])

        # Filter out duplicates
        unique_documents = []
        for doc in new_documents:
            content_hash = self._get_content_hash(doc.text)

            if content_hash not in existing_hashes:
                # Add hash to metadata for future deduplication
                doc.metadata["content_hash"] = content_hash
                unique_documents.append(doc)
                existing_hashes.add(content_hash)

        return unique_documents
