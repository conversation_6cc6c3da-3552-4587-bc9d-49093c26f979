"""
🧠 CrewAI OSINT Agent Framework - Stateful Geopolitical Agent

Enhanced geopolitical agent with memory persistence and stateful conversations
using LangGraph integration for context-aware analysis.
"""

import json
from datetime import datetime
from typing import Dict, List, Any, Optional
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from agents.geo_agent import GeopoliticalAgent
from utils.memory_manager import StatefulAgent, memory_manager
from utils.logging_config import osint_logger, log_execution_time


class StatefulGeopoliticalAgent(StatefulAgent):
    """Geopolitical agent with memory and conversation state"""
    
    def __init__(self, user_id: str = "default"):
        super().__init__(agent_type="geopolitical", user_id=user_id)
        self.base_agent = GeopoliticalAgent()
        self.analysis_history = []
        
        osint_logger.logger.info(
            "Stateful geopolitical agent initialized",
            user_id=user_id
        )
    
    @log_execution_time("stateful_geo_agent")
    def analyze_with_context(self, query: str, regions: List[str] = None, 
                           analysis_type: str = "intelligence_brief") -> Dict[str, Any]:
        """Perform analysis with conversation context"""
        
        # Start conversation if not already started
        if not self.conversation_id:
            initial_context = {
                "analysis_type": analysis_type,
                "regions": regions or [],
                "started_at": datetime.now().isoformat()
            }
            self.start_conversation(initial_context)
        
        # Add user query to conversation
        self.add_user_message(
            content=query,
            metadata={
                "regions": regions,
                "analysis_type": analysis_type
            }
        )
        
        # Get conversation context for enhanced analysis
        context = self.get_conversation_context()
        
        # Enhance query with historical context
        enhanced_query = self._enhance_query_with_context(query, context)
        
        # Perform analysis
        try:
            if analysis_type == "intelligence_brief":
                result = self.base_agent.generate_intelligence_brief(
                    topic=enhanced_query,
                    regions=regions or ["Global"],
                    time_range="30d"
                )
            elif analysis_type == "regional_monitoring":
                result = self.base_agent.setup_regional_monitoring(
                    regions=regions or ["Global"],
                    keywords=[query],
                    frequency="daily"
                )
            elif analysis_type == "situation_report":
                result = self._generate_contextual_situation_report(
                    query, regions, context
                )
            else:
                result = self.base_agent.generate_intelligence_brief(
                    topic=enhanced_query,
                    regions=regions or ["Global"]
                )
            
            # Add analysis metadata
            result["conversation_context"] = {
                "conversation_id": self.conversation_id,
                "message_count": context.get("message_count", 0),
                "relevant_memories": len(context.get("relevant_memories", [])),
                "analysis_type": analysis_type
            }
            
            # Store analysis result
            self.add_agent_response(
                content=f"Analysis completed for: {query}",
                analysis_data=result
            )
            
            # Update conversation context
            self.update_context({
                "last_analysis": {
                    "query": query,
                    "type": analysis_type,
                    "regions": regions,
                    "timestamp": datetime.now().isoformat()
                }
            })
            
            # Store in analysis history
            self.analysis_history.append({
                "query": query,
                "result": result,
                "timestamp": datetime.now().isoformat(),
                "conversation_id": self.conversation_id
            })
            
            osint_logger.logger.info(
                "Contextual analysis completed",
                query=query[:100],
                analysis_type=analysis_type,
                conversation_id=self.conversation_id
            )
            
            return result
            
        except Exception as e:
            osint_logger.log_error(e, "stateful_geo_analysis", {
                "query": query,
                "analysis_type": analysis_type,
                "conversation_id": self.conversation_id
            })
            
            error_result = {
                "error": str(e),
                "query": query,
                "analysis_type": analysis_type,
                "timestamp": datetime.now().isoformat()
            }
            
            self.add_agent_response(
                content=f"Analysis failed for: {query}. Error: {str(e)}",
                analysis_data=error_result
            )
            
            return error_result
    
    def _enhance_query_with_context(self, query: str, context: Dict[str, Any]) -> str:
        """Enhance query with conversation context"""
        
        enhanced_query = query
        
        # Add context from recent messages
        recent_messages = context.get("recent_messages", [])
        if recent_messages:
            # Extract topics from recent user messages
            recent_topics = []
            for msg in recent_messages[-3:]:  # Last 3 messages
                if msg.get("role") == "user":
                    recent_topics.append(msg.get("content", ""))
            
            if recent_topics:
                enhanced_query += f"\n\nContext from recent conversation: {'; '.join(recent_topics)}"
        
        # Add context from relevant memories
        relevant_memories = context.get("relevant_memories", [])
        if relevant_memories:
            memory_context = []
            for memory in relevant_memories[:2]:  # Top 2 relevant memories
                if memory.get("content_type") == "analysis":
                    try:
                        analysis_data = json.loads(memory.get("content", "{}"))
                        if "summary" in analysis_data:
                            memory_context.append(analysis_data["summary"])
                    except:
                        pass
            
            if memory_context:
                enhanced_query += f"\n\nRelevant previous analysis: {'; '.join(memory_context)}"
        
        return enhanced_query
    
    def _generate_contextual_situation_report(self, query: str, regions: List[str], 
                                            context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate situation report with conversation context"""
        
        # Get base situation report
        base_report = self.base_agent.generate_multi_region_situation_report(
            regions=regions or ["Global"],
            time_range="24h"
        )
        
        # Enhance with conversation context
        contextual_insights = []
        
        # Analyze conversation patterns
        recent_messages = context.get("recent_messages", [])
        if len(recent_messages) > 1:
            contextual_insights.append(
                f"This is part of an ongoing conversation with {len(recent_messages)} messages. "
                f"User has been focusing on: {query}"
            )
        
        # Add relevant historical analysis
        relevant_memories = context.get("relevant_memories", [])
        for memory in relevant_memories:
            if memory.get("content_type") == "analysis":
                try:
                    analysis_data = json.loads(memory.get("content", "{}"))
                    if "key_findings" in analysis_data:
                        contextual_insights.append(
                            f"Previous analysis found: {analysis_data['key_findings'][:200]}..."
                        )
                except:
                    pass
        
        # Add contextual insights to report
        if contextual_insights:
            base_report["contextual_insights"] = contextual_insights
            base_report["conversation_enhanced"] = True
        
        return base_report
    
    def get_analysis_summary(self, limit: int = 5) -> Dict[str, Any]:
        """Get summary of recent analyses"""
        
        recent_analyses = self.analysis_history[-limit:]
        
        summary = {
            "total_analyses": len(self.analysis_history),
            "recent_analyses": len(recent_analyses),
            "conversation_id": self.conversation_id,
            "analyses": []
        }
        
        for analysis in recent_analyses:
            summary["analyses"].append({
                "query": analysis["query"],
                "timestamp": analysis["timestamp"],
                "has_result": "error" not in analysis["result"]
            })
        
        return summary
    
    def search_analysis_history(self, search_query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Search through analysis history"""
        
        if not self.conversation_id:
            return []
        
        # Search conversation memories
        memories = self.search_conversation_history(search_query, limit)
        
        # Filter for analysis memories
        analysis_memories = [
            memory for memory in memories
            if memory.get("content_type") == "analysis"
        ]
        
        # Convert to analysis format
        results = []
        for memory in analysis_memories:
            try:
                analysis_data = json.loads(memory.get("content", "{}"))
                results.append({
                    "timestamp": memory.get("timestamp"),
                    "analysis_data": analysis_data,
                    "importance": memory.get("importance"),
                    "tags": memory.get("tags", [])
                })
            except:
                continue
        
        return results
    
    def get_conversation_insights(self) -> Dict[str, Any]:
        """Get insights about the current conversation"""
        
        if not self.conversation_id:
            return {"error": "No active conversation"}
        
        context = self.get_conversation_context()
        
        insights = {
            "conversation_id": self.conversation_id,
            "message_count": context.get("message_count", 0),
            "analysis_count": len(self.analysis_history),
            "topics_discussed": [],
            "regions_analyzed": set(),
            "analysis_types_used": set()
        }
        
        # Analyze conversation patterns
        recent_messages = context.get("recent_messages", [])
        for msg in recent_messages:
            if msg.get("role") == "user":
                insights["topics_discussed"].append(msg.get("content", "")[:100])
            
            metadata = msg.get("metadata", {})
            if "regions" in metadata:
                insights["regions_analyzed"].update(metadata["regions"])
            if "analysis_type" in metadata:
                insights["analysis_types_used"].add(metadata["analysis_type"])
        
        # Convert sets to lists for JSON serialization
        insights["regions_analyzed"] = list(insights["regions_analyzed"])
        insights["analysis_types_used"] = list(insights["analysis_types_used"])
        
        return insights
    
    def export_conversation(self) -> Dict[str, Any]:
        """Export complete conversation data"""
        
        if not self.conversation_id:
            return {"error": "No active conversation"}
        
        conversation = memory_manager.get_conversation(self.conversation_id)
        if not conversation:
            return {"error": "Conversation not found"}
        
        # Get all memories for this conversation
        memories = memory_manager.get_relevant_memories(
            self.conversation_id, limit=1000
        )
        
        export_data = {
            "conversation_id": self.conversation_id,
            "user_id": self.user_id,
            "agent_type": self.agent_type,
            "created_at": conversation.created_at,
            "updated_at": conversation.updated_at,
            "messages": conversation.messages,
            "context": conversation.context,
            "memories": [
                {
                    "content": memory.content,
                    "content_type": memory.content_type,
                    "importance": memory.importance,
                    "timestamp": memory.timestamp,
                    "tags": memory.tags
                }
                for memory in memories
            ],
            "analysis_history": self.analysis_history,
            "insights": self.get_conversation_insights()
        }
        
        return export_data
