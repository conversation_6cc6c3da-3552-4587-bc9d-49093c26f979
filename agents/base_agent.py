"""
Base OSINT Agent class providing common functionality for all specialized agents.

This module provides the foundational BaseOSINTAgent class that integrates
CrewAI, LangChain, LlamaIndex, and DSPy for comprehensive intelligence
gathering and analysis capabilities.
"""

import os
import logging
import sys
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
from pathlib import Path

import dspy
from crewai import Agent, Task, Crew
from crewai.tools import BaseTool
from langchain_openai import ChatOpenAI
from llama_index.core import VectorStoreIndex, Document
from llama_index.embeddings.openai import OpenAIEmbedding

# Add utils to path for error handling
sys.path.append(str(Path(__file__).parent.parent))
from utils.error_handling import (
    require_api_keys,
    handle_exceptions,
    validate_input,
    log_performance,
    AgentError,
    APIKeyError,
    retry_on_failure
)
from utils.config_validator import check_quick_status
from tools.serper_wrapper import SerperSearchTool
from tools.llamaindex_tools import LlamaIndexRAGTool
from tools.crawl4ai_wrapper import <PERSON>raw<PERSON><PERSON><PERSON><PERSON>ool
from rag.index_builder import IndexBuilder


class BaseOSINTAgent(ABC):
    """
    Base class for OSINT agents providing common functionality.

    This abstract base class integrates CrewAI, LangChain, LlamaIndex, and DSPy
    for comprehensive intelligence gathering and analysis capabilities. It provides
    common functionality for all specialized OSINT agents including tool management,
    knowledge base building, and task execution.

    Attributes:
        name (str): Agent name identifier
        role (str): Agent role description
        goal (str): Agent's primary objective
        backstory (str): Agent's background story for context
        verbose (bool): Enable verbose logging
        llm (ChatOpenAI): Language model instance
        tools (List[BaseTool]): Available tools for the agent
        vector_index (Optional[VectorStoreIndex]): Vector index for RAG
        agent (Agent): CrewAI agent instance
        logger (logging.Logger): Logger instance
    """

    def __init__(
        self,
        name: str,
        role: str,
        goal: str,
        backstory: str,
        llm_model: str = "gpt-4",
        temperature: float = 0.1,
        max_tokens: int = 2000,
        verbose: bool = True
    ) -> None:
        """
        Initialize the base OSINT agent.

        Args:
            name: Unique identifier for the agent
            role: Description of the agent's role and expertise
            goal: Primary objective the agent is designed to achieve
            backstory: Background story providing context for the agent
            llm_model: Language model to use (default: "gpt-4")
            temperature: LLM temperature setting for response randomness (0.0-1.0)
            max_tokens: Maximum tokens for LLM responses
            verbose: Enable detailed logging output

        Raises:
            AgentError: If configuration validation fails or initialization errors occur
            APIKeyError: If required API keys are missing
        """
        # Validate configuration before initialization
        self._validate_configuration(name)

        # Initialize instance attributes
        self.name = name
        self.role = role
        self.goal = goal
        self.backstory = backstory
        self.verbose = verbose

        # Setup logging first
        self._setup_logging()

        # Initialize LLM with error handling
        self.llm = self._initialize_llm(llm_model, temperature, max_tokens)

        # Initialize DSPy configuration
        self._configure_dspy(llm_model)

        # Initialize tools
        self.tools = self._initialize_tools()

        # Initialize RAG components
        self.index_builder = IndexBuilder()
        self.vector_index: Optional[VectorStoreIndex] = None

        # Initialize CrewAI agent
        self.agent = self._create_crewai_agent()

        self.logger.info(f"Agent '{name}' initialized successfully with {len(self.tools)} tools")

    @handle_exceptions()
    def _validate_configuration(self, name: str) -> None:
        """
        Validate system configuration before agent initialization.

        Args:
            name: Agent name for error reporting

        Raises:
            AgentError: If configuration validation fails
        """
        is_ready, issues = check_quick_status()
        if not is_ready:
            raise AgentError(
                f"Agent initialization failed due to configuration issues: {'; '.join(issues)}",
                agent_type=name
            )

    def _initialize_llm(self, llm_model: str, temperature: float, max_tokens: int) -> ChatOpenAI:
        """
        Initialize the language model with error handling.

        Args:
            llm_model: Model name to use
            temperature: Temperature setting
            max_tokens: Maximum tokens

        Returns:
            Initialized ChatOpenAI instance

        Raises:
            AgentError: If LLM initialization fails
        """
        try:
            llm = ChatOpenAI(
                model=llm_model,
                temperature=temperature,
                max_tokens=max_tokens
            )
            self.logger.info(f"Initialized LLM with model: {llm_model}")
            return llm
        except Exception as e:
            raise AgentError(f"Failed to initialize LLM: {str(e)}", agent_type=self.name)

    def _configure_dspy(self, llm_model: str) -> None:
        """
        Configure DSPy with the language model.

        Args:
            llm_model: Model name to configure
        """
        try:
            dspy_lm = dspy.LM(model=f"openai/{llm_model}", api_key=os.getenv("OPENAI_API_KEY"))
            dspy.settings.configure(lm=dspy_lm)
            self.logger.info("DSPy configuration successful")
        except Exception as e:
            self.logger.warning(f"DSPy configuration failed: {e}. DSPy features may not work.")
    
    def _setup_logging(self) -> None:
        """
        Setup logging configuration for the agent.

        Configures logging level based on verbose setting and creates
        a logger instance specific to this agent.
        """
        logging.basicConfig(
            level=logging.INFO if self.verbose else logging.WARNING,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(self.name)
    
    @handle_exceptions()
    def _initialize_tools(self) -> List[BaseTool]:
        """
        Initialize the tools available to this agent.

        Attempts to initialize core tools (Serper, LlamaIndex RAG, Crawl4AI)
        and agent-specific tools. Gracefully handles missing dependencies
        and API keys.

        Returns:
            List of successfully initialized tools
        """
        tools: List[BaseTool] = []

        # Initialize core tools with individual error handling
        tools.extend(self._initialize_serper_tool())
        tools.extend(self._initialize_rag_tool())
        tools.extend(self._initialize_crawl_tool())
        tools.extend(self._initialize_specialized_tools())

        self.logger.info(f"Initialized {len(tools)} tools successfully")
        return tools

    def _initialize_serper_tool(self) -> List[BaseTool]:
        """Initialize Serper search tool if API key is available."""
        if not os.getenv("SERPER_API_KEY"):
            self.logger.warning("Serper API key not found, skipping search tool")
            return []

        try:
            tool = SerperSearchTool()
            self.logger.info("Serper search tool initialized")
            return [tool]
        except Exception as e:
            self.logger.error(f"Failed to initialize Serper tool: {str(e)}")
            return []

    def _initialize_rag_tool(self) -> List[BaseTool]:
        """Initialize LlamaIndex RAG tool."""
        try:
            tool = LlamaIndexRAGTool()
            self.logger.info("LlamaIndex RAG tool initialized")
            return [tool]
        except Exception as e:
            self.logger.error(f"Failed to initialize LlamaIndex RAG tool: {str(e)}")
            return []

    def _initialize_crawl_tool(self) -> List[BaseTool]:
        """Initialize Crawl4AI web crawling tool."""
        try:
            tool = Crawl4AITool()
            self.logger.info("Crawl4AI tool initialized")
            return [tool]
        except ImportError:
            self.logger.warning("Crawl4AI not available, skipping web crawler tool")
            return []
        except Exception as e:
            self.logger.error(f"Failed to initialize Crawl4AI tool: {str(e)}")
            return []

    def _initialize_specialized_tools(self) -> List[BaseTool]:
        """Initialize agent-specific tools."""
        try:
            specialized_tools = self._get_specialized_tools()
            self.logger.info(f"Added {len(specialized_tools)} specialized tools")
            return specialized_tools
        except Exception as e:
            self.logger.error(f"Failed to initialize specialized tools: {str(e)}")
            return []
    
    @abstractmethod
    def _get_specialized_tools(self) -> List[BaseTool]:
        """
        Get tools specific to this agent type.

        This method must be implemented by subclasses to provide
        agent-specific tools and capabilities.

        Returns:
            List of specialized tools for this agent type
        """
        pass

    def _create_crewai_agent(self) -> Agent:
        """
        Create the CrewAI agent instance with configured tools and LLM.

        Returns:
            Configured CrewAI Agent instance
        """
        return Agent(
            role=self.role,
            goal=self.goal,
            backstory=self.backstory,
            tools=self.tools,
            llm=self.llm,
            verbose=self.verbose,
            allow_delegation=False,
            max_iter=3
        )
    
    @handle_exceptions()
    @validate_input({
        'documents': {'required': True, 'type': list, 'min_length': 1},
        'urls': {'type': list}
    })
    @log_performance
    def build_knowledge_base(self, documents: List[str], urls: Optional[List[str]] = None) -> None:
        """
        Build a knowledge base from documents and URLs for RAG capabilities.

        Creates a vector index from the provided documents and optionally
        crawled URLs, then updates any RAG tools with the new index.

        Args:
            documents: List of document texts to index
            urls: Optional list of URLs to crawl and index

        Raises:
            AgentError: If knowledge base building fails
        """
        self.logger.info(f"Building knowledge base with {len(documents)} documents")

        if urls:
            self.logger.info(f"Also processing {len(urls)} URLs")

        try:
            # Convert texts to LlamaIndex documents
            docs = [Document(text=doc) for doc in documents]

            # Build vector index
            self.vector_index = self.index_builder.build_index(docs, urls)

            # Update RAG tool with new index
            self._update_rag_tools_with_index()

            self.logger.info("Knowledge base built successfully")

        except Exception as e:
            raise AgentError(f"Failed to build knowledge base: {str(e)}", agent_type=self.name)

    def _update_rag_tools_with_index(self) -> None:
        """Update RAG tools with the current vector index."""
        if not self.vector_index:
            return

        updated_count = 0
        for tool in self.tools:
            if isinstance(tool, LlamaIndexRAGTool):
                tool.set_index(self.vector_index)
                updated_count += 1

        if updated_count > 0:
            self.logger.info(f"Updated {updated_count} RAG tools with new index")
    
    @handle_exceptions()
    @require_api_keys(['OPENAI_API_KEY'])
    @validate_input({
        'task_description': {'required': True, 'type': str, 'min_length': 10},
        'context': {'type': dict}
    })
    @log_performance
    @retry_on_failure(max_retries=2)
    def execute_task(self, task_description: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Execute a task using the agent with CrewAI orchestration.

        Creates a CrewAI task and crew to execute the given task description,
        returning structured results with metadata and timing information.

        Args:
            task_description: Detailed description of the task to execute
            context: Optional context information to inform the task

        Returns:
            Dictionary containing:
                - task: Original task description
                - result: Task execution results
                - agent: Agent name
                - execution_time: Time taken in seconds
                - timestamp: ISO format timestamp
                - context: Provided context
                - status: Execution status

        Raises:
            AgentError: If task execution fails
        """
        self.logger.info(f"Executing task: {task_description[:100]}...")

        try:
            # Create CrewAI task with enhanced configuration
            task = Task(
                description=task_description,
                agent=self.agent,
                expected_output="Structured intelligence report with sources and analysis"
            )

            # Create crew and execute
            crew = Crew(
                agents=[self.agent],
                tasks=[task],
                verbose=self.verbose
            )

            start_time = datetime.now()
            result = crew.kickoff()
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()

            self.logger.info(f"Task completed successfully in {execution_time:.2f} seconds")

            # Package results with comprehensive metadata
            return {
                "task": task_description,
                "result": result,
                "agent": self.name,
                "execution_time": execution_time,
                "timestamp": end_time.isoformat(),
                "context": context or {},
                "status": "success"
            }

        except Exception as e:
            self.logger.error(f"Task execution failed: {str(e)}")
            raise AgentError(f"Task execution failed: {str(e)}", agent_type=self.name)
    
    @abstractmethod
    def analyze(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Perform specialized analysis based on agent type.

        This method must be implemented by subclasses to provide
        domain-specific analysis capabilities.

        Args:
            query: Analysis query or topic
            **kwargs: Additional parameters specific to the agent type

        Returns:
            Dictionary containing analysis results, metadata, and insights
        """
        pass

    def get_capabilities(self) -> Dict[str, Any]:
        """
        Get comprehensive information about agent capabilities and status.

        Returns:
            Dictionary containing:
                - name: Agent identifier
                - role: Agent role description
                - goal: Agent's primary objective
                - tools: List of available tool names
                - has_knowledge_base: Whether vector index is available
                - llm_model: Language model being used
                - tool_count: Number of available tools
                - initialization_status: Agent readiness status
        """
        return {
            "name": self.name,
            "role": self.role,
            "goal": self.goal,
            "tools": [tool.__class__.__name__ for tool in self.tools],
            "tool_count": len(self.tools),
            "has_knowledge_base": self.vector_index is not None,
            "llm_model": getattr(self.llm, 'model_name', 'unknown'),
            "initialization_status": "ready" if self.tools else "limited"
        }
