"""
🧠 CrewAI OSINT Agent Framework - Configuration Validator

Comprehensive configuration validation and environment setup verification.
"""

import os
import sys
import importlib
from typing import Dict, List, Tuple, Any, Optional
from pathlib import Path
import json
import logging
from dataclasses import dataclass

# Load environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

from .error_handling import ValidationError, OSINTFrameworkError

logger = logging.getLogger(__name__)

@dataclass
class ValidationResult:
    """Result of a validation check"""
    is_valid: bool
    message: str
    details: Optional[Dict[str, Any]] = None
    severity: str = "error"  # error, warning, info

class ConfigValidator:
    """Comprehensive configuration validator for the OSINT framework"""
    
    def __init__(self):
        self.results: List[ValidationResult] = []
        self.project_root = Path(__file__).parent.parent
    
    def validate_all(self) -> Dict[str, Any]:
        """
        Run all validation checks
        
        Returns:
            Comprehensive validation report
        """
        self.results = []
        
        # Core validations
        self.validate_python_version()
        self.validate_api_keys()
        self.validate_dependencies()
        self.validate_project_structure()
        self.validate_environment_file()
        self.validate_data_directories()
        self.validate_permissions()
        
        # Generate report
        return self.generate_report()
    
    def validate_python_version(self):
        """Validate Python version compatibility"""
        min_version = (3, 8)
        current_version = sys.version_info[:2]
        
        if current_version >= min_version:
            self.results.append(ValidationResult(
                is_valid=True,
                message=f"Python version {'.'.join(map(str, current_version))} is compatible",
                severity="info"
            ))
        else:
            self.results.append(ValidationResult(
                is_valid=False,
                message=f"Python version {'.'.join(map(str, current_version))} is not supported. Minimum required: {'.'.join(map(str, min_version))}",
                details={"current_version": current_version, "min_version": min_version}
            ))
    
    def validate_api_keys(self):
        """Validate API key configuration"""
        required_keys = {
            'OPENAI_API_KEY': {
                'description': 'OpenAI API key for LLM operations',
                'pattern': r'^sk-[a-zA-Z0-9_-]{20,}$',
                'required': True
            },
            'SERPER_API_KEY': {
                'description': 'Serper.dev API key for web search',
                'pattern': r'^[a-f0-9]{32,}$',
                'required': True
            }
        }
        
        for key_name, config in required_keys.items():
            key_value = os.getenv(key_name)
            
            if not key_value:
                if config['required']:
                    self.results.append(ValidationResult(
                        is_valid=False,
                        message=f"Required API key '{key_name}' is not configured",
                        details={"key": key_name, "description": config['description']}
                    ))
                else:
                    self.results.append(ValidationResult(
                        is_valid=True,
                        message=f"Optional API key '{key_name}' is not configured",
                        severity="warning"
                    ))
            else:
                # Validate key format if pattern is provided
                if 'pattern' in config:
                    import re
                    if re.match(config['pattern'], key_value):
                        self.results.append(ValidationResult(
                            is_valid=True,
                            message=f"API key '{key_name}' format is valid",
                            severity="info"
                        ))
                    else:
                        self.results.append(ValidationResult(
                            is_valid=False,
                            message=f"API key '{key_name}' format is invalid",
                            details={"key": key_name, "expected_pattern": config['pattern']}
                        ))
                else:
                    self.results.append(ValidationResult(
                        is_valid=True,
                        message=f"API key '{key_name}' is configured",
                        severity="info"
                    ))
    
    def validate_dependencies(self):
        """Validate required Python packages"""
        required_packages = {
            'crewai': 'CrewAI framework',
            'langchain': 'LangChain framework',
            'llama_index': 'LlamaIndex framework',
            'dspy': 'DSPy framework',
            'openai': 'OpenAI client',
            'requests': 'HTTP requests',
            'pandas': 'Data manipulation',
            'numpy': 'Numerical computing',
            'streamlit': 'Web UI framework',
            'fastapi': 'API framework',
            'uvicorn': 'ASGI server'
        }
        
        missing_packages = []
        installed_packages = []
        
        for package, description in required_packages.items():
            try:
                importlib.import_module(package)
                installed_packages.append(package)
                self.results.append(ValidationResult(
                    is_valid=True,
                    message=f"Package '{package}' is installed",
                    severity="info"
                ))
            except ImportError:
                missing_packages.append(package)
                self.results.append(ValidationResult(
                    is_valid=False,
                    message=f"Required package '{package}' is not installed",
                    details={"package": package, "description": description}
                ))
        
        if missing_packages:
            self.results.append(ValidationResult(
                is_valid=False,
                message=f"Missing {len(missing_packages)} required packages",
                details={"missing_packages": missing_packages}
            ))
    
    def validate_project_structure(self):
        """Validate project directory structure"""
        required_dirs = [
            'agents',
            'tools',
            'rag',
            'workflows',
            'examples',
            'data',
            'output',
            'ui',
            'api'
        ]
        
        required_files = [
            'requirements.txt',
            'setup.py',
            'README.md',
            'agents/__init__.py',
            'tools/__init__.py',
            'rag/__init__.py',
            'workflows/__init__.py'
        ]
        
        # Check directories
        for dir_name in required_dirs:
            dir_path = self.project_root / dir_name
            if dir_path.exists() and dir_path.is_dir():
                self.results.append(ValidationResult(
                    is_valid=True,
                    message=f"Directory '{dir_name}' exists",
                    severity="info"
                ))
            else:
                self.results.append(ValidationResult(
                    is_valid=False,
                    message=f"Required directory '{dir_name}' is missing",
                    details={"directory": dir_name, "path": str(dir_path)}
                ))
        
        # Check files
        for file_name in required_files:
            file_path = self.project_root / file_name
            if file_path.exists() and file_path.is_file():
                self.results.append(ValidationResult(
                    is_valid=True,
                    message=f"File '{file_name}' exists",
                    severity="info"
                ))
            else:
                self.results.append(ValidationResult(
                    is_valid=False,
                    message=f"Required file '{file_name}' is missing",
                    details={"file": file_name, "path": str(file_path)}
                ))
    
    def validate_environment_file(self):
        """Validate .env file configuration"""
        env_file = self.project_root / ".env"
        
        if not env_file.exists():
            self.results.append(ValidationResult(
                is_valid=False,
                message="Environment file (.env) is missing",
                details={"path": str(env_file)}
            ))
            return
        
        try:
            env_content = env_file.read_text()
            
            # Check for required environment variables
            required_vars = ['OPENAI_API_KEY', 'SERPER_API_KEY']
            found_vars = []
            
            for var in required_vars:
                if f"{var}=" in env_content:
                    found_vars.append(var)
            
            missing_vars = set(required_vars) - set(found_vars)
            
            if missing_vars:
                self.results.append(ValidationResult(
                    is_valid=False,
                    message=f"Environment file is missing variables: {', '.join(missing_vars)}",
                    details={"missing_variables": list(missing_vars)}
                ))
            else:
                self.results.append(ValidationResult(
                    is_valid=True,
                    message="Environment file contains all required variables",
                    severity="info"
                ))
                
        except Exception as e:
            self.results.append(ValidationResult(
                is_valid=False,
                message=f"Failed to read environment file: {str(e)}",
                details={"error": str(e)}
            ))
    
    def validate_data_directories(self):
        """Validate data storage directories"""
        data_dirs = [
            'data/crawled_docs',
            'data/cti_index',
            'data/vector_index',
            'output/reports'
        ]
        
        for dir_name in data_dirs:
            dir_path = self.project_root / dir_name
            
            # Create directory if it doesn't exist
            try:
                dir_path.mkdir(parents=True, exist_ok=True)
                self.results.append(ValidationResult(
                    is_valid=True,
                    message=f"Data directory '{dir_name}' is available",
                    severity="info"
                ))
            except Exception as e:
                self.results.append(ValidationResult(
                    is_valid=False,
                    message=f"Failed to create data directory '{dir_name}': {str(e)}",
                    details={"directory": dir_name, "error": str(e)}
                ))
    
    def validate_permissions(self):
        """Validate file system permissions"""
        test_dirs = [
            self.project_root / "data",
            self.project_root / "output"
        ]
        
        for test_dir in test_dirs:
            try:
                # Test write permission
                test_file = test_dir / "permission_test.tmp"
                test_file.write_text("test")
                test_file.unlink()
                
                self.results.append(ValidationResult(
                    is_valid=True,
                    message=f"Write permissions OK for '{test_dir.name}'",
                    severity="info"
                ))
                
            except Exception as e:
                self.results.append(ValidationResult(
                    is_valid=False,
                    message=f"Insufficient permissions for '{test_dir.name}': {str(e)}",
                    details={"directory": str(test_dir), "error": str(e)}
                ))
    
    def generate_report(self) -> Dict[str, Any]:
        """Generate comprehensive validation report"""
        errors = [r for r in self.results if not r.is_valid]
        warnings = [r for r in self.results if r.is_valid and r.severity == "warning"]
        info = [r for r in self.results if r.is_valid and r.severity == "info"]
        
        report = {
            "timestamp": str(Path(__file__).stat().st_mtime),
            "summary": {
                "total_checks": len(self.results),
                "errors": len(errors),
                "warnings": len(warnings),
                "info": len(info),
                "overall_status": "PASS" if len(errors) == 0 else "FAIL"
            },
            "results": {
                "errors": [self._result_to_dict(r) for r in errors],
                "warnings": [self._result_to_dict(r) for r in warnings],
                "info": [self._result_to_dict(r) for r in info]
            },
            "recommendations": self._generate_recommendations(errors, warnings)
        }
        
        return report
    
    def _result_to_dict(self, result: ValidationResult) -> Dict[str, Any]:
        """Convert ValidationResult to dictionary"""
        return {
            "message": result.message,
            "details": result.details,
            "severity": result.severity
        }
    
    def _generate_recommendations(self, errors: List[ValidationResult], warnings: List[ValidationResult]) -> List[str]:
        """Generate recommendations based on validation results"""
        recommendations = []
        
        if errors:
            recommendations.append("❌ Critical issues found that must be resolved before using the framework")
            
            # API key recommendations
            api_key_errors = [e for e in errors if "API key" in e.message]
            if api_key_errors:
                recommendations.append("🔑 Create a .env file with your API keys:")
                recommendations.append("   OPENAI_API_KEY=your_openai_key")
                recommendations.append("   SERPER_API_KEY=your_serper_key")
            
            # Package recommendations
            package_errors = [e for e in errors if "package" in e.message]
            if package_errors:
                recommendations.append("📦 Install missing packages with: pip install -r requirements.txt")
            
            # Structure recommendations
            structure_errors = [e for e in errors if "directory" in e.message or "file" in e.message]
            if structure_errors:
                recommendations.append("📁 Ensure you're running from the correct project directory")
        
        if warnings:
            recommendations.append("⚠️ Some optional components are not configured")
        
        if not errors and not warnings:
            recommendations.append("✅ All checks passed! The framework is ready to use.")
        
        return recommendations

def validate_framework_config() -> Dict[str, Any]:
    """
    Convenience function to validate the entire framework configuration
    
    Returns:
        Validation report
    """
    validator = ConfigValidator()
    return validator.validate_all()

def check_quick_status() -> Tuple[bool, List[str]]:
    """
    Quick status check for essential components
    
    Returns:
        Tuple of (is_ready, issues)
    """
    issues = []
    
    # Check API keys
    if not os.getenv('OPENAI_API_KEY'):
        issues.append("OpenAI API key not configured")
    
    if not os.getenv('SERPER_API_KEY'):
        issues.append("Serper API key not configured")
    
    # Check critical packages
    critical_packages = ['openai', 'requests', 'pandas']
    for package in critical_packages:
        try:
            importlib.import_module(package)
        except ImportError:
            issues.append(f"Critical package '{package}' not installed")
    
    return len(issues) == 0, issues

if __name__ == "__main__":
    # Run validation when script is executed directly
    report = validate_framework_config()
    print(json.dumps(report, indent=2))
