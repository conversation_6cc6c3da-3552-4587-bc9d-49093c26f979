"""
🧠 CrewAI OSINT Agent Framework - Advanced Logging Configuration

Comprehensive logging system with structured logging, metrics collection,
and monitoring capabilities for production environments.
"""

import logging
import logging.config
import json
import time
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
from functools import wraps
import traceback

# Third-party imports for advanced logging
try:
    import structlog
    STRUCTLOG_AVAILABLE = True
except ImportError:
    STRUCTLOG_AVAILABLE = False

try:
    from prometheus_client import Counter, Histogram, Gauge, start_http_server
    PROMETHEUS_AVAILABLE = True
except ImportError:
    PROMETHEUS_AVAILABLE = False


class OSINTLogger:
    """Advanced logger for OSINT framework with structured logging and metrics"""
    
    def __init__(self, name: str = "osint_framework"):
        self.name = name
        self.setup_logging()
        self.setup_metrics()
        
    def setup_logging(self):
        """Setup structured logging configuration"""
        
        # Create logs directory
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # Logging configuration
        log_level = os.getenv("LOG_LEVEL", "INFO").upper()
        debug_mode = os.getenv("DEBUG_MODE", "false").lower() == "true"
        
        if debug_mode:
            log_level = "DEBUG"
        
        # Configure structlog if available
        if STRUCTLOG_AVAILABLE:
            structlog.configure(
                processors=[
                    structlog.stdlib.filter_by_level,
                    structlog.stdlib.add_logger_name,
                    structlog.stdlib.add_log_level,
                    structlog.stdlib.PositionalArgumentsFormatter(),
                    structlog.processors.TimeStamper(fmt="iso"),
                    structlog.processors.StackInfoRenderer(),
                    structlog.processors.format_exc_info,
                    structlog.processors.UnicodeDecoder(),
                    structlog.processors.JSONRenderer()
                ],
                context_class=dict,
                logger_factory=structlog.stdlib.LoggerFactory(),
                wrapper_class=structlog.stdlib.BoundLogger,
                cache_logger_on_first_use=True,
            )
            self.logger = structlog.get_logger(self.name)
        else:
            # Fallback to standard logging
            logging.basicConfig(
                level=getattr(logging, log_level),
                format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                handlers=[
                    logging.FileHandler(log_dir / "osint_framework.log"),
                    logging.StreamHandler(sys.stdout)
                ]
            )
            self.logger = logging.getLogger(self.name)
    
    def setup_metrics(self):
        """Setup Prometheus metrics if available"""
        
        if PROMETHEUS_AVAILABLE:
            # Request metrics
            self.request_count = Counter(
                'osint_requests_total',
                'Total number of requests',
                ['method', 'endpoint', 'status']
            )
            
            self.request_duration = Histogram(
                'osint_request_duration_seconds',
                'Request duration in seconds',
                ['method', 'endpoint']
            )
            
            # Analysis metrics
            self.analysis_count = Counter(
                'osint_analysis_total',
                'Total number of analyses',
                ['agent_type', 'analysis_type', 'status']
            )
            
            self.analysis_duration = Histogram(
                'osint_analysis_duration_seconds',
                'Analysis duration in seconds',
                ['agent_type', 'analysis_type']
            )
            
            # System metrics
            self.active_tasks = Gauge(
                'osint_active_tasks',
                'Number of active tasks'
            )
            
            self.error_count = Counter(
                'osint_errors_total',
                'Total number of errors',
                ['error_type', 'component']
            )
            
            # Start metrics server
            metrics_port = int(os.getenv("METRICS_PORT", "9090"))
            try:
                start_http_server(metrics_port)
                self.logger.info(f"Metrics server started on port {metrics_port}")
            except Exception as e:
                self.logger.warning(f"Failed to start metrics server: {e}")
        else:
            # Create dummy metrics for compatibility
            self.request_count = DummyMetric()
            self.request_duration = DummyMetric()
            self.analysis_count = DummyMetric()
            self.analysis_duration = DummyMetric()
            self.active_tasks = DummyMetric()
            self.error_count = DummyMetric()
    
    def log_request(self, method: str, endpoint: str, status_code: int, duration: float):
        """Log HTTP request with metrics"""
        
        self.logger.info(
            "HTTP request",
            method=method,
            endpoint=endpoint,
            status_code=status_code,
            duration=duration
        )
        
        if PROMETHEUS_AVAILABLE:
            self.request_count.labels(
                method=method,
                endpoint=endpoint,
                status=str(status_code)
            ).inc()
            
            self.request_duration.labels(
                method=method,
                endpoint=endpoint
            ).observe(duration)
    
    def log_analysis_start(self, agent_type: str, analysis_type: str, task_id: str):
        """Log analysis start"""
        
        self.logger.info(
            "Analysis started",
            agent_type=agent_type,
            analysis_type=analysis_type,
            task_id=task_id
        )
        
        if PROMETHEUS_AVAILABLE:
            self.active_tasks.inc()
    
    def log_analysis_complete(self, agent_type: str, analysis_type: str, task_id: str, 
                            duration: float, status: str = "success"):
        """Log analysis completion"""
        
        self.logger.info(
            "Analysis completed",
            agent_type=agent_type,
            analysis_type=analysis_type,
            task_id=task_id,
            duration=duration,
            status=status
        )
        
        if PROMETHEUS_AVAILABLE:
            self.analysis_count.labels(
                agent_type=agent_type,
                analysis_type=analysis_type,
                status=status
            ).inc()
            
            self.analysis_duration.labels(
                agent_type=agent_type,
                analysis_type=analysis_type
            ).observe(duration)
            
            self.active_tasks.dec()
    
    def log_error(self, error: Exception, component: str, context: Dict[str, Any] = None):
        """Log error with context"""
        
        error_context = {
            "error_type": type(error).__name__,
            "error_message": str(error),
            "component": component,
            "traceback": traceback.format_exc()
        }
        
        if context:
            error_context.update(context)
        
        self.logger.error("Error occurred", **error_context)
        
        if PROMETHEUS_AVAILABLE:
            self.error_count.labels(
                error_type=type(error).__name__,
                component=component
            ).inc()


class DummyMetric:
    """Dummy metric class for when Prometheus is not available"""
    
    def labels(self, **kwargs):
        return self
    
    def inc(self, value=1):
        pass
    
    def dec(self, value=1):
        pass
    
    def observe(self, value):
        pass


# Global logger instance
osint_logger = OSINTLogger()


def log_execution_time(component: str = "unknown"):
    """Decorator to log function execution time"""
    
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                
                osint_logger.logger.debug(
                    "Function executed",
                    function=func.__name__,
                    component=component,
                    duration=duration,
                    status="success"
                )
                
                return result
                
            except Exception as e:
                duration = time.time() - start_time
                
                osint_logger.log_error(
                    e, 
                    component,
                    {
                        "function": func.__name__,
                        "duration": duration,
                        "args": str(args)[:200],  # Truncate for privacy
                        "kwargs": str(kwargs)[:200]
                    }
                )
                raise
        
        return wrapper
    return decorator


def log_api_call(func):
    """Decorator to log API calls with metrics"""
    
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        
        # Extract request info (this is FastAPI specific)
        method = "unknown"
        endpoint = func.__name__
        status_code = 200
        
        try:
            result = await func(*args, **kwargs)
            duration = time.time() - start_time
            
            osint_logger.log_request(method, endpoint, status_code, duration)
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            status_code = 500
            
            osint_logger.log_request(method, endpoint, status_code, duration)
            osint_logger.log_error(e, "api", {"endpoint": endpoint})
            raise
    
    return wrapper


class PerformanceMonitor:
    """Monitor system performance and resource usage"""
    
    def __init__(self):
        self.start_time = time.time()
        self.request_times = []
        
    def record_request_time(self, duration: float):
        """Record request processing time"""
        self.request_times.append(duration)
        
        # Keep only last 1000 requests
        if len(self.request_times) > 1000:
            self.request_times = self.request_times[-1000:]
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics"""
        
        if not self.request_times:
            return {"status": "no_data"}
        
        import statistics
        
        return {
            "uptime_seconds": time.time() - self.start_time,
            "total_requests": len(self.request_times),
            "avg_response_time": statistics.mean(self.request_times),
            "median_response_time": statistics.median(self.request_times),
            "p95_response_time": statistics.quantiles(self.request_times, n=20)[18] if len(self.request_times) > 20 else 0,
            "min_response_time": min(self.request_times),
            "max_response_time": max(self.request_times)
        }


# Global performance monitor
performance_monitor = PerformanceMonitor()


def setup_logging_for_module(module_name: str) -> logging.Logger:
    """Setup logging for a specific module"""
    
    logger = logging.getLogger(f"osint_framework.{module_name}")
    
    if not logger.handlers:
        # Create logs directory
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # File handler for module-specific logs
        file_handler = logging.FileHandler(log_dir / f"{module_name}.log")
        file_handler.setFormatter(
            logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
        )
        
        logger.addHandler(file_handler)
        logger.setLevel(logging.INFO)
    
    return logger
