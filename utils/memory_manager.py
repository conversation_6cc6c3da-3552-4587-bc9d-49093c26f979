"""
🧠 CrewAI OSINT Agent Framework - Memory Management with LangGraph

Advanced memory system using LangGraph for persistent conversations,
context management, and stateful agent interactions.
"""

import json
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
import uuid
from dataclasses import dataclass, asdict
import pickle
import hashlib

try:
    from langgraph.graph import StateGraph, END
    from langgraph.checkpoint.sqlite import SqliteSaver
    from langgraph.checkpoint.memory import MemorySaver
    LANGGRAPH_AVAILABLE = True
except ImportError:
    LANGGRAPH_AVAILABLE = False

from .logging_config import osint_logger


@dataclass
class ConversationState:
    """State object for conversation management"""
    conversation_id: str
    user_id: str
    agent_type: str
    messages: List[Dict[str, Any]]
    context: Dict[str, Any]
    metadata: Dict[str, Any]
    created_at: str
    updated_at: str
    active: bool = True


@dataclass
class MemoryEntry:
    """Individual memory entry"""
    id: str
    conversation_id: str
    content: str
    content_type: str  # 'message', 'analysis', 'context', 'metadata'
    importance: float  # 0.0 to 1.0
    timestamp: str
    tags: List[str]
    embedding: Optional[List[float]] = None


class MemoryManager:
    """Advanced memory management system with LangGraph integration"""
    
    def __init__(self, db_path: str = "data/memory.db"):
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        self.conversations: Dict[str, ConversationState] = {}
        self.memory_entries: Dict[str, MemoryEntry] = {}
        
        self.setup_database()
        self.setup_langgraph()
        
        osint_logger.logger.info("Memory manager initialized")
    
    def setup_database(self):
        """Setup SQLite database for persistent storage"""
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Conversations table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS conversations (
                    id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    agent_type TEXT NOT NULL,
                    messages TEXT NOT NULL,
                    context TEXT NOT NULL,
                    metadata TEXT NOT NULL,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    active BOOLEAN DEFAULT TRUE
                )
            """)
            
            # Memory entries table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS memory_entries (
                    id TEXT PRIMARY KEY,
                    conversation_id TEXT NOT NULL,
                    content TEXT NOT NULL,
                    content_type TEXT NOT NULL,
                    importance REAL NOT NULL,
                    timestamp TEXT NOT NULL,
                    tags TEXT NOT NULL,
                    embedding BLOB,
                    FOREIGN KEY (conversation_id) REFERENCES conversations (id)
                )
            """)
            
            # Indexes for better performance
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_conversations_user_id ON conversations(user_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_conversations_agent_type ON conversations(agent_type)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_memory_entries_conversation_id ON memory_entries(conversation_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_memory_entries_content_type ON memory_entries(content_type)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_memory_entries_importance ON memory_entries(importance)")
            
            conn.commit()
    
    def setup_langgraph(self):
        """Setup LangGraph for stateful conversations"""
        
        if not LANGGRAPH_AVAILABLE:
            osint_logger.logger.warning("LangGraph not available, using fallback memory system")
            self.checkpointer = None
            return
        
        # Use SQLite checkpointer for persistence
        self.checkpointer = SqliteSaver.from_conn_string(f"sqlite:///{self.db_path}")
        
        # Create conversation graph
        self.conversation_graph = self.create_conversation_graph()
        
        osint_logger.logger.info("LangGraph memory system initialized")
    
    def create_conversation_graph(self):
        """Create LangGraph conversation flow"""
        
        if not LANGGRAPH_AVAILABLE:
            return None
        
        def process_message(state: Dict[str, Any]) -> Dict[str, Any]:
            """Process incoming message and update state"""
            
            conversation_id = state.get("conversation_id")
            message = state.get("current_message")
            
            if conversation_id and message:
                # Store message in memory
                self.add_memory_entry(
                    conversation_id=conversation_id,
                    content=message.get("content", ""),
                    content_type="message",
                    importance=0.7,
                    tags=message.get("tags", [])
                )
                
                # Update conversation state
                if conversation_id in self.conversations:
                    conv = self.conversations[conversation_id]
                    conv.messages.append(message)
                    conv.updated_at = datetime.now().isoformat()
                    self.save_conversation(conv)
            
            return state
        
        def analyze_context(state: Dict[str, Any]) -> Dict[str, Any]:
            """Analyze conversation context and extract insights"""
            
            conversation_id = state.get("conversation_id")
            
            if conversation_id:
                # Get relevant memories
                memories = self.get_relevant_memories(
                    conversation_id=conversation_id,
                    query=state.get("current_message", {}).get("content", ""),
                    limit=10
                )
                
                # Update context
                state["relevant_memories"] = [asdict(memory) for memory in memories]
                state["context_updated"] = True
            
            return state
        
        def generate_response(state: Dict[str, Any]) -> Dict[str, Any]:
            """Generate response based on context and memories"""
            
            # This would integrate with the actual agent logic
            # For now, we just mark that response generation is ready
            state["response_ready"] = True
            
            return state
        
        # Build the graph
        workflow = StateGraph(dict)
        
        workflow.add_node("process_message", process_message)
        workflow.add_node("analyze_context", analyze_context)
        workflow.add_node("generate_response", generate_response)
        
        workflow.set_entry_point("process_message")
        workflow.add_edge("process_message", "analyze_context")
        workflow.add_edge("analyze_context", "generate_response")
        workflow.add_edge("generate_response", END)
        
        return workflow.compile(checkpointer=self.checkpointer)
    
    def create_conversation(self, user_id: str, agent_type: str, 
                          initial_context: Dict[str, Any] = None) -> str:
        """Create a new conversation"""
        
        conversation_id = str(uuid.uuid4())
        now = datetime.now().isoformat()
        
        conversation = ConversationState(
            conversation_id=conversation_id,
            user_id=user_id,
            agent_type=agent_type,
            messages=[],
            context=initial_context or {},
            metadata={},
            created_at=now,
            updated_at=now
        )
        
        self.conversations[conversation_id] = conversation
        self.save_conversation(conversation)
        
        osint_logger.logger.info(
            "New conversation created",
            conversation_id=conversation_id,
            user_id=user_id,
            agent_type=agent_type
        )
        
        return conversation_id
    
    def add_message(self, conversation_id: str, message: Dict[str, Any]) -> bool:
        """Add a message to a conversation"""
        
        if conversation_id not in self.conversations:
            osint_logger.logger.warning(f"Conversation {conversation_id} not found")
            return False
        
        # Add timestamp if not present
        if "timestamp" not in message:
            message["timestamp"] = datetime.now().isoformat()
        
        # Process through LangGraph if available
        if self.conversation_graph:
            try:
                state = {
                    "conversation_id": conversation_id,
                    "current_message": message
                }
                
                # Run through the graph
                result = self.conversation_graph.invoke(
                    state,
                    config={"configurable": {"thread_id": conversation_id}}
                )
                
                return True
                
            except Exception as e:
                osint_logger.log_error(e, "langgraph_processing")
                # Fallback to direct processing
        
        # Direct processing fallback
        conversation = self.conversations[conversation_id]
        conversation.messages.append(message)
        conversation.updated_at = datetime.now().isoformat()
        
        # Store as memory entry
        self.add_memory_entry(
            conversation_id=conversation_id,
            content=message.get("content", ""),
            content_type="message",
            importance=0.7,
            tags=message.get("tags", [])
        )
        
        self.save_conversation(conversation)
        return True
    
    def add_memory_entry(self, conversation_id: str, content: str, 
                        content_type: str, importance: float = 0.5,
                        tags: List[str] = None) -> str:
        """Add a memory entry"""
        
        entry_id = str(uuid.uuid4())
        
        memory_entry = MemoryEntry(
            id=entry_id,
            conversation_id=conversation_id,
            content=content,
            content_type=content_type,
            importance=importance,
            timestamp=datetime.now().isoformat(),
            tags=tags or []
        )
        
        self.memory_entries[entry_id] = memory_entry
        self.save_memory_entry(memory_entry)
        
        return entry_id
    
    def get_conversation(self, conversation_id: str) -> Optional[ConversationState]:
        """Get a conversation by ID"""
        
        if conversation_id in self.conversations:
            return self.conversations[conversation_id]
        
        # Try loading from database
        return self.load_conversation(conversation_id)
    
    def get_conversation_history(self, conversation_id: str, 
                               limit: int = 50) -> List[Dict[str, Any]]:
        """Get conversation message history"""
        
        conversation = self.get_conversation(conversation_id)
        if not conversation:
            return []
        
        return conversation.messages[-limit:]
    
    def get_relevant_memories(self, conversation_id: str, query: str = "",
                            content_type: str = None, limit: int = 10) -> List[MemoryEntry]:
        """Get relevant memories for a conversation"""
        
        # Load memories if not in cache
        self.load_memory_entries(conversation_id)
        
        # Filter memories for this conversation
        conversation_memories = [
            memory for memory in self.memory_entries.values()
            if memory.conversation_id == conversation_id
        ]
        
        # Filter by content type if specified
        if content_type:
            conversation_memories = [
                memory for memory in conversation_memories
                if memory.content_type == content_type
            ]
        
        # Sort by importance and recency
        conversation_memories.sort(
            key=lambda x: (x.importance, x.timestamp),
            reverse=True
        )
        
        return conversation_memories[:limit]
    
    def search_memories(self, query: str, conversation_id: str = None,
                       limit: int = 20) -> List[MemoryEntry]:
        """Search memories by content"""
        
        # Simple text search (could be enhanced with embeddings)
        query_lower = query.lower()
        
        matching_memories = []
        for memory in self.memory_entries.values():
            if conversation_id and memory.conversation_id != conversation_id:
                continue
            
            if query_lower in memory.content.lower():
                matching_memories.append(memory)
        
        # Sort by importance
        matching_memories.sort(key=lambda x: x.importance, reverse=True)
        
        return matching_memories[:limit]
    
    def update_conversation_context(self, conversation_id: str, 
                                  context_updates: Dict[str, Any]):
        """Update conversation context"""
        
        conversation = self.get_conversation(conversation_id)
        if not conversation:
            return False
        
        conversation.context.update(context_updates)
        conversation.updated_at = datetime.now().isoformat()
        
        self.save_conversation(conversation)
        return True
    
    def save_conversation(self, conversation: ConversationState):
        """Save conversation to database"""
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT OR REPLACE INTO conversations 
                (id, user_id, agent_type, messages, context, metadata, created_at, updated_at, active)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                conversation.conversation_id,
                conversation.user_id,
                conversation.agent_type,
                json.dumps(conversation.messages),
                json.dumps(conversation.context),
                json.dumps(conversation.metadata),
                conversation.created_at,
                conversation.updated_at,
                conversation.active
            ))
            
            conn.commit()
    
    def load_conversation(self, conversation_id: str) -> Optional[ConversationState]:
        """Load conversation from database"""
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT id, user_id, agent_type, messages, context, metadata, 
                       created_at, updated_at, active
                FROM conversations WHERE id = ?
            """, (conversation_id,))
            
            row = cursor.fetchone()
            if not row:
                return None
            
            conversation = ConversationState(
                conversation_id=row[0],
                user_id=row[1],
                agent_type=row[2],
                messages=json.loads(row[3]),
                context=json.loads(row[4]),
                metadata=json.loads(row[5]),
                created_at=row[6],
                updated_at=row[7],
                active=bool(row[8])
            )
            
            self.conversations[conversation_id] = conversation
            return conversation
    
    def save_memory_entry(self, memory_entry: MemoryEntry):
        """Save memory entry to database"""
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            embedding_blob = None
            if memory_entry.embedding:
                embedding_blob = pickle.dumps(memory_entry.embedding)
            
            cursor.execute("""
                INSERT OR REPLACE INTO memory_entries 
                (id, conversation_id, content, content_type, importance, timestamp, tags, embedding)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                memory_entry.id,
                memory_entry.conversation_id,
                memory_entry.content,
                memory_entry.content_type,
                memory_entry.importance,
                memory_entry.timestamp,
                json.dumps(memory_entry.tags),
                embedding_blob
            ))
            
            conn.commit()
    
    def load_memory_entries(self, conversation_id: str):
        """Load memory entries for a conversation"""
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT id, conversation_id, content, content_type, importance, 
                       timestamp, tags, embedding
                FROM memory_entries WHERE conversation_id = ?
            """, (conversation_id,))
            
            for row in cursor.fetchall():
                embedding = None
                if row[7]:
                    embedding = pickle.loads(row[7])
                
                memory_entry = MemoryEntry(
                    id=row[0],
                    conversation_id=row[1],
                    content=row[2],
                    content_type=row[3],
                    importance=row[4],
                    timestamp=row[5],
                    tags=json.loads(row[6]),
                    embedding=embedding
                )
                
                self.memory_entries[memory_entry.id] = memory_entry
    
    def cleanup_old_conversations(self, days: int = 30):
        """Clean up old inactive conversations"""
        
        cutoff_date = datetime.now() - timedelta(days=days)
        cutoff_str = cutoff_date.isoformat()
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Get old conversation IDs
            cursor.execute("""
                SELECT id FROM conversations 
                WHERE updated_at < ? AND active = FALSE
            """, (cutoff_str,))
            
            old_conversation_ids = [row[0] for row in cursor.fetchall()]
            
            # Delete memory entries
            for conv_id in old_conversation_ids:
                cursor.execute("DELETE FROM memory_entries WHERE conversation_id = ?", (conv_id,))
            
            # Delete conversations
            cursor.execute("""
                DELETE FROM conversations 
                WHERE updated_at < ? AND active = FALSE
            """, (cutoff_str,))
            
            conn.commit()
            
            osint_logger.logger.info(f"Cleaned up {len(old_conversation_ids)} old conversations")
    
    def get_conversation_stats(self) -> Dict[str, Any]:
        """Get memory system statistics"""
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Conversation stats
            cursor.execute("SELECT COUNT(*) FROM conversations WHERE active = TRUE")
            active_conversations = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM conversations")
            total_conversations = cursor.fetchone()[0]
            
            # Memory stats
            cursor.execute("SELECT COUNT(*) FROM memory_entries")
            total_memories = cursor.fetchone()[0]
            
            cursor.execute("SELECT AVG(importance) FROM memory_entries")
            avg_importance = cursor.fetchone()[0] or 0
            
            return {
                "active_conversations": active_conversations,
                "total_conversations": total_conversations,
                "total_memories": total_memories,
                "average_importance": avg_importance,
                "langgraph_enabled": LANGGRAPH_AVAILABLE
            }


# Global memory manager instance
memory_manager = MemoryManager()


class StatefulAgent:
    """Base class for stateful agents with memory integration"""

    def __init__(self, agent_type: str, user_id: str = "default"):
        self.agent_type = agent_type
        self.user_id = user_id
        self.conversation_id = None
        self.memory_manager = memory_manager

    def start_conversation(self, initial_context: Dict[str, Any] = None) -> str:
        """Start a new conversation"""
        self.conversation_id = self.memory_manager.create_conversation(
            user_id=self.user_id,
            agent_type=self.agent_type,
            initial_context=initial_context
        )
        return self.conversation_id

    def continue_conversation(self, conversation_id: str) -> bool:
        """Continue an existing conversation"""
        conversation = self.memory_manager.get_conversation(conversation_id)
        if conversation and conversation.active:
            self.conversation_id = conversation_id
            return True
        return False

    def add_user_message(self, content: str, metadata: Dict[str, Any] = None) -> bool:
        """Add a user message to the conversation"""
        if not self.conversation_id:
            return False

        message = {
            "role": "user",
            "content": content,
            "metadata": metadata or {},
            "timestamp": datetime.now().isoformat()
        }

        return self.memory_manager.add_message(self.conversation_id, message)

    def add_agent_response(self, content: str, analysis_data: Dict[str, Any] = None) -> bool:
        """Add an agent response to the conversation"""
        if not self.conversation_id:
            return False

        message = {
            "role": "assistant",
            "content": content,
            "analysis_data": analysis_data or {},
            "timestamp": datetime.now().isoformat()
        }

        # Store the response
        success = self.memory_manager.add_message(self.conversation_id, message)

        # Store analysis data as high-importance memory if present
        if analysis_data and success:
            self.memory_manager.add_memory_entry(
                conversation_id=self.conversation_id,
                content=json.dumps(analysis_data),
                content_type="analysis",
                importance=0.9,
                tags=["analysis", self.agent_type]
            )

        return success

    def get_conversation_context(self) -> Dict[str, Any]:
        """Get current conversation context"""
        if not self.conversation_id:
            return {}

        conversation = self.memory_manager.get_conversation(self.conversation_id)
        if not conversation:
            return {}

        # Get recent messages
        recent_messages = self.memory_manager.get_conversation_history(
            self.conversation_id, limit=10
        )

        # Get relevant memories
        relevant_memories = self.memory_manager.get_relevant_memories(
            self.conversation_id, limit=5
        )

        return {
            "conversation_id": self.conversation_id,
            "agent_type": self.agent_type,
            "context": conversation.context,
            "recent_messages": recent_messages,
            "relevant_memories": [asdict(memory) for memory in relevant_memories],
            "message_count": len(conversation.messages)
        }

    def update_context(self, context_updates: Dict[str, Any]) -> bool:
        """Update conversation context"""
        if not self.conversation_id:
            return False

        return self.memory_manager.update_conversation_context(
            self.conversation_id, context_updates
        )

    def search_conversation_history(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Search conversation history"""
        if not self.conversation_id:
            return []

        memories = self.memory_manager.search_memories(
            query=query,
            conversation_id=self.conversation_id,
            limit=limit
        )

        return [asdict(memory) for memory in memories]
