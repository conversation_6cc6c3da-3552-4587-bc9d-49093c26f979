"""
🧠 CrewAI OSINT Agent Framework - Error Handling and Validation

Comprehensive error handling, validation, and recovery mechanisms for the OSINT framework.
"""

import os
import logging
import traceback
from typing import Optional, Dict, Any, List
from functools import wraps
from datetime import datetime
import json
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('osint_framework.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class OSINTFrameworkError(Exception):
    """Base exception for OSINT Framework"""
    def __init__(self, message: str, error_code: str = None, details: Dict[str, Any] = None):
        self.message = message
        self.error_code = error_code or "OSINT_ERROR"
        self.details = details or {}
        self.timestamp = datetime.now().isoformat()
        super().__init__(self.message)

class APIKeyError(OSINTFrameworkError):
    """Exception for API key related issues"""
    def __init__(self, message: str, missing_keys: List[str] = None):
        super().__init__(message, "API_KEY_ERROR", {"missing_keys": missing_keys or []})

class AgentError(OSINTFrameworkError):
    """Exception for agent-related issues"""
    def __init__(self, message: str, agent_type: str = None):
        super().__init__(message, "AGENT_ERROR", {"agent_type": agent_type})

class WorkflowError(OSINTFrameworkError):
    """Exception for workflow-related issues"""
    def __init__(self, message: str, workflow_type: str = None, step: str = None):
        super().__init__(message, "WORKFLOW_ERROR", {"workflow_type": workflow_type, "step": step})

class DataProcessingError(OSINTFrameworkError):
    """Exception for data processing issues"""
    def __init__(self, message: str, data_type: str = None):
        super().__init__(message, "DATA_PROCESSING_ERROR", {"data_type": data_type})

class ExternalServiceError(OSINTFrameworkError):
    """Exception for external service issues"""
    def __init__(self, message: str, service: str = None, status_code: int = None):
        super().__init__(message, "EXTERNAL_SERVICE_ERROR", {"service": service, "status_code": status_code})

class ValidationError(OSINTFrameworkError):
    """Exception for validation issues"""
    def __init__(self, message: str, field: str = None, value: Any = None):
        super().__init__(message, "VALIDATION_ERROR", {"field": field, "value": str(value) if value else None})

def validate_api_keys() -> Dict[str, bool]:
    """
    Validate that required API keys are configured
    
    Returns:
        Dict with validation status for each key
    """
    required_keys = {
        'OPENAI_API_KEY': os.getenv('OPENAI_API_KEY'),
        'SERPER_API_KEY': os.getenv('SERPER_API_KEY')
    }
    
    validation_results = {}
    missing_keys = []
    
    for key_name, key_value in required_keys.items():
        is_valid = bool(key_value and len(key_value.strip()) > 0)
        validation_results[key_name] = is_valid
        
        if not is_valid:
            missing_keys.append(key_name)
    
    if missing_keys:
        logger.warning(f"Missing API keys: {missing_keys}")
    
    return validation_results

def require_api_keys(keys: List[str] = None):
    """
    Decorator to ensure required API keys are configured
    
    Args:
        keys: List of required API keys. If None, checks all standard keys.
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            if keys is None:
                validation_results = validate_api_keys()
                missing = [k for k, v in validation_results.items() if not v]
            else:
                missing = []
                for key in keys:
                    if not os.getenv(key):
                        missing.append(key)
            
            if missing:
                raise APIKeyError(
                    f"Required API keys not configured: {', '.join(missing)}",
                    missing_keys=missing
                )
            
            return func(*args, **kwargs)
        return wrapper
    return decorator

def handle_exceptions(error_handler=None, reraise=True):
    """
    Decorator for comprehensive exception handling
    
    Args:
        error_handler: Custom error handler function
        reraise: Whether to reraise the exception after handling
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except OSINTFrameworkError as e:
                # Framework-specific errors
                logger.error(f"OSINT Framework Error in {func.__name__}: {e.message}")
                logger.error(f"Error details: {e.details}")
                
                if error_handler:
                    error_handler(e)
                
                if reraise:
                    raise
                return None
                
            except Exception as e:
                # Unexpected errors
                error_msg = f"Unexpected error in {func.__name__}: {str(e)}"
                logger.error(error_msg)
                logger.error(f"Traceback: {traceback.format_exc()}")
                
                framework_error = OSINTFrameworkError(
                    error_msg,
                    "UNEXPECTED_ERROR",
                    {"original_error": str(e), "function": func.__name__}
                )
                
                if error_handler:
                    error_handler(framework_error)
                
                if reraise:
                    raise framework_error
                return None
        return wrapper
    return decorator

def validate_input(validation_rules: Dict[str, Any]):
    """
    Decorator for input validation
    
    Args:
        validation_rules: Dictionary of validation rules
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Validate arguments
            for param_name, rules in validation_rules.items():
                if param_name in kwargs:
                    value = kwargs[param_name]
                    
                    # Required check
                    if rules.get('required', False) and (value is None or value == ""):
                        raise ValidationError(
                            f"Parameter '{param_name}' is required",
                            field=param_name,
                            value=value
                        )
                    
                    # Type check
                    if 'type' in rules and value is not None:
                        expected_type = rules['type']
                        if not isinstance(value, expected_type):
                            raise ValidationError(
                                f"Parameter '{param_name}' must be of type {expected_type.__name__}",
                                field=param_name,
                                value=value
                            )
                    
                    # Length check for strings
                    if 'min_length' in rules and isinstance(value, str):
                        if len(value) < rules['min_length']:
                            raise ValidationError(
                                f"Parameter '{param_name}' must be at least {rules['min_length']} characters",
                                field=param_name,
                                value=value
                            )
                    
                    if 'max_length' in rules and isinstance(value, str):
                        if len(value) > rules['max_length']:
                            raise ValidationError(
                                f"Parameter '{param_name}' must be at most {rules['max_length']} characters",
                                field=param_name,
                                value=value
                            )
                    
                    # Choices check
                    if 'choices' in rules and value is not None:
                        if value not in rules['choices']:
                            raise ValidationError(
                                f"Parameter '{param_name}' must be one of: {rules['choices']}",
                                field=param_name,
                                value=value
                            )
            
            return func(*args, **kwargs)
        return wrapper
    return decorator

def retry_on_failure(max_retries: int = 3, delay: float = 1.0, backoff: float = 2.0):
    """
    Decorator for retrying failed operations
    
    Args:
        max_retries: Maximum number of retry attempts
        delay: Initial delay between retries (seconds)
        backoff: Backoff multiplier for delay
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            current_delay = delay
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except (ExternalServiceError, ConnectionError, TimeoutError) as e:
                    last_exception = e
                    
                    if attempt < max_retries:
                        logger.warning(f"Attempt {attempt + 1} failed for {func.__name__}: {str(e)}")
                        logger.info(f"Retrying in {current_delay} seconds...")
                        
                        import time
                        time.sleep(current_delay)
                        current_delay *= backoff
                    else:
                        logger.error(f"All {max_retries + 1} attempts failed for {func.__name__}")
                        raise last_exception
                except Exception as e:
                    # Don't retry for other types of exceptions
                    raise e
            
            # This should never be reached, but just in case
            raise last_exception
        return wrapper
    return decorator

def log_performance(func):
    """Decorator to log function performance"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = datetime.now()
        
        try:
            result = func(*args, **kwargs)
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            logger.info(f"Function {func.__name__} completed in {duration:.2f} seconds")
            return result
            
        except Exception as e:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            logger.error(f"Function {func.__name__} failed after {duration:.2f} seconds: {str(e)}")
            raise
    
    return wrapper

def safe_json_loads(json_str: str, default: Any = None) -> Any:
    """
    Safely parse JSON string with error handling
    
    Args:
        json_str: JSON string to parse
        default: Default value to return on error
    
    Returns:
        Parsed JSON or default value
    """
    try:
        return json.loads(json_str)
    except (json.JSONDecodeError, TypeError) as e:
        logger.warning(f"Failed to parse JSON: {str(e)}")
        return default

def safe_file_operation(operation: str, file_path: str, content: str = None) -> Optional[str]:
    """
    Safely perform file operations with error handling
    
    Args:
        operation: 'read' or 'write'
        file_path: Path to the file
        content: Content to write (for write operations)
    
    Returns:
        File content (for read operations) or None on error
    """
    try:
        path = Path(file_path)
        
        if operation == 'read':
            if not path.exists():
                logger.warning(f"File does not exist: {file_path}")
                return None
            
            return path.read_text(encoding='utf-8')
            
        elif operation == 'write':
            if content is None:
                raise ValueError("Content is required for write operations")
            
            # Create parent directories if they don't exist
            path.parent.mkdir(parents=True, exist_ok=True)
            
            path.write_text(content, encoding='utf-8')
            logger.info(f"Successfully wrote to file: {file_path}")
            return content
            
        else:
            raise ValueError(f"Unknown operation: {operation}")
            
    except Exception as e:
        logger.error(f"File operation '{operation}' failed for {file_path}: {str(e)}")
        return None

def create_error_report(error: Exception, context: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Create a detailed error report
    
    Args:
        error: The exception that occurred
        context: Additional context information
    
    Returns:
        Detailed error report
    """
    report = {
        "timestamp": datetime.now().isoformat(),
        "error_type": type(error).__name__,
        "error_message": str(error),
        "traceback": traceback.format_exc(),
        "context": context or {}
    }
    
    if isinstance(error, OSINTFrameworkError):
        report.update({
            "error_code": error.error_code,
            "details": error.details
        })
    
    return report

def save_error_report(error: Exception, context: Dict[str, Any] = None, file_path: str = None) -> str:
    """
    Save error report to file
    
    Args:
        error: The exception that occurred
        context: Additional context information
        file_path: Path to save the report (optional)
    
    Returns:
        Path to the saved report
    """
    report = create_error_report(error, context)
    
    if file_path is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_path = f"error_reports/error_report_{timestamp}.json"
    
    report_json = json.dumps(report, indent=2)
    safe_file_operation('write', file_path, report_json)
    
    return file_path
