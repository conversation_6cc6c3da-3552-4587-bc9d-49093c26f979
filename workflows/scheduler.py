"""
🧠 CrewAI OSINT Agent Framework - Workflow Scheduler

Event-driven workflow scheduler with cron-like scheduling, event triggers,
and batch processing capabilities for automated OSINT operations.
"""

import asyncio
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from pathlib import Path
import sys
from dataclasses import dataclass
from enum import Enum
import croniter
import uuid

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from workflows.workflow_engine import workflow_engine, WorkflowStatus
from utils.logging_config import osint_logger


class TriggerType(Enum):
    """Types of workflow triggers"""
    SCHEDULE = "schedule"
    EVENT = "event"
    WEBHOOK = "webhook"
    FILE_WATCH = "file_watch"
    MANUAL = "manual"


@dataclass
class ScheduledWorkflow:
    """Scheduled workflow configuration"""
    workflow_id: str
    trigger_type: TriggerType
    schedule: Optional[str] = None  # Cron expression
    event_type: Optional[str] = None
    webhook_path: Optional[str] = None
    file_path: Optional[str] = None
    enabled: bool = True
    last_execution: Optional[datetime] = None
    next_execution: Optional[datetime] = None
    execution_count: int = 0
    max_executions: Optional[int] = None
    context: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.context is None:
            self.context = {}


class WorkflowScheduler:
    """Advanced workflow scheduler with multiple trigger types"""
    
    def __init__(self):
        self.scheduled_workflows: Dict[str, ScheduledWorkflow] = {}
        self.event_listeners: Dict[str, List[str]] = {}  # event_type -> workflow_ids
        self.webhook_handlers: Dict[str, str] = {}  # webhook_path -> workflow_id
        self.file_watchers: Dict[str, str] = {}  # file_path -> workflow_id
        
        self.running = False
        self.scheduler_task: Optional[asyncio.Task] = None
        
        osint_logger.logger.info("Workflow scheduler initialized")
    
    def schedule_workflow(self, workflow_id: str, trigger_type: TriggerType,
                         schedule: Optional[str] = None,
                         event_type: Optional[str] = None,
                         webhook_path: Optional[str] = None,
                         file_path: Optional[str] = None,
                         context: Dict[str, Any] = None,
                         max_executions: Optional[int] = None) -> str:
        """Schedule a workflow with specified trigger"""
        
        schedule_id = str(uuid.uuid4())
        
        scheduled_workflow = ScheduledWorkflow(
            workflow_id=workflow_id,
            trigger_type=trigger_type,
            schedule=schedule,
            event_type=event_type,
            webhook_path=webhook_path,
            file_path=file_path,
            context=context or {},
            max_executions=max_executions
        )
        
        # Calculate next execution for scheduled workflows
        if trigger_type == TriggerType.SCHEDULE and schedule:
            try:
                cron = croniter.croniter(schedule, datetime.now())
                scheduled_workflow.next_execution = cron.get_next(datetime)
            except Exception as e:
                osint_logger.log_error(e, "schedule_parsing", {"schedule": schedule})
                raise ValueError(f"Invalid cron schedule: {schedule}")
        
        self.scheduled_workflows[schedule_id] = scheduled_workflow
        
        # Register event listener
        if trigger_type == TriggerType.EVENT and event_type:
            if event_type not in self.event_listeners:
                self.event_listeners[event_type] = []
            self.event_listeners[event_type].append(schedule_id)
        
        # Register webhook handler
        if trigger_type == TriggerType.WEBHOOK and webhook_path:
            self.webhook_handlers[webhook_path] = schedule_id
        
        # Register file watcher
        if trigger_type == TriggerType.FILE_WATCH and file_path:
            self.file_watchers[file_path] = schedule_id
        
        osint_logger.logger.info(
            "Workflow scheduled",
            workflow_id=workflow_id,
            schedule_id=schedule_id,
            trigger_type=trigger_type.value
        )
        
        return schedule_id
    
    def unschedule_workflow(self, schedule_id: str) -> bool:
        """Unschedule a workflow"""
        
        if schedule_id not in self.scheduled_workflows:
            return False
        
        scheduled_workflow = self.scheduled_workflows[schedule_id]
        
        # Remove from event listeners
        if scheduled_workflow.event_type:
            event_type = scheduled_workflow.event_type
            if event_type in self.event_listeners:
                self.event_listeners[event_type] = [
                    sid for sid in self.event_listeners[event_type] 
                    if sid != schedule_id
                ]
                if not self.event_listeners[event_type]:
                    del self.event_listeners[event_type]
        
        # Remove from webhook handlers
        if scheduled_workflow.webhook_path:
            webhook_path = scheduled_workflow.webhook_path
            if webhook_path in self.webhook_handlers:
                del self.webhook_handlers[webhook_path]
        
        # Remove from file watchers
        if scheduled_workflow.file_path:
            file_path = scheduled_workflow.file_path
            if file_path in self.file_watchers:
                del self.file_watchers[file_path]
        
        del self.scheduled_workflows[schedule_id]
        
        osint_logger.logger.info(f"Workflow unscheduled: {schedule_id}")
        return True
    
    async def start(self):
        """Start the scheduler"""
        
        if self.running:
            return
        
        self.running = True
        self.scheduler_task = asyncio.create_task(self._scheduler_loop())
        
        osint_logger.logger.info("Workflow scheduler started")
    
    async def stop(self):
        """Stop the scheduler"""
        
        if not self.running:
            return
        
        self.running = False
        
        if self.scheduler_task:
            self.scheduler_task.cancel()
            try:
                await self.scheduler_task
            except asyncio.CancelledError:
                pass
        
        osint_logger.logger.info("Workflow scheduler stopped")
    
    async def _scheduler_loop(self):
        """Main scheduler loop"""
        
        while self.running:
            try:
                await self._check_scheduled_workflows()
                await asyncio.sleep(60)  # Check every minute
            except asyncio.CancelledError:
                break
            except Exception as e:
                osint_logger.log_error(e, "scheduler_loop")
                await asyncio.sleep(60)
    
    async def _check_scheduled_workflows(self):
        """Check and execute scheduled workflows"""
        
        now = datetime.now()
        
        for schedule_id, scheduled_workflow in self.scheduled_workflows.items():
            if not scheduled_workflow.enabled:
                continue
            
            if scheduled_workflow.trigger_type != TriggerType.SCHEDULE:
                continue
            
            if not scheduled_workflow.next_execution:
                continue
            
            if now >= scheduled_workflow.next_execution:
                # Check max executions
                if (scheduled_workflow.max_executions and 
                    scheduled_workflow.execution_count >= scheduled_workflow.max_executions):
                    scheduled_workflow.enabled = False
                    osint_logger.logger.info(
                        f"Workflow {schedule_id} disabled - max executions reached"
                    )
                    continue
                
                # Execute workflow
                await self._execute_scheduled_workflow(schedule_id, scheduled_workflow)
                
                # Calculate next execution
                if scheduled_workflow.schedule:
                    try:
                        cron = croniter.croniter(scheduled_workflow.schedule, now)
                        scheduled_workflow.next_execution = cron.get_next(datetime)
                    except Exception as e:
                        osint_logger.log_error(e, "next_execution_calculation")
                        scheduled_workflow.enabled = False
    
    async def _execute_scheduled_workflow(self, schedule_id: str, 
                                        scheduled_workflow: ScheduledWorkflow):
        """Execute a scheduled workflow"""
        
        try:
            # Add scheduling context
            execution_context = scheduled_workflow.context.copy()
            execution_context.update({
                "schedule_id": schedule_id,
                "trigger_type": scheduled_workflow.trigger_type.value,
                "execution_count": scheduled_workflow.execution_count + 1,
                "scheduled_at": scheduled_workflow.next_execution.isoformat() if scheduled_workflow.next_execution else None
            })
            
            # Execute workflow
            execution_id = await workflow_engine.execute_workflow(
                workflow_id=scheduled_workflow.workflow_id,
                initial_context=execution_context
            )
            
            # Update scheduled workflow
            scheduled_workflow.last_execution = datetime.now()
            scheduled_workflow.execution_count += 1
            
            osint_logger.logger.info(
                "Scheduled workflow executed",
                schedule_id=schedule_id,
                workflow_id=scheduled_workflow.workflow_id,
                execution_id=execution_id
            )
            
        except Exception as e:
            osint_logger.log_error(e, "scheduled_workflow_execution", {
                "schedule_id": schedule_id,
                "workflow_id": scheduled_workflow.workflow_id
            })
    
    async def trigger_event(self, event_type: str, event_data: Dict[str, Any] = None):
        """Trigger workflows based on event"""
        
        if event_type not in self.event_listeners:
            return
        
        event_data = event_data or {}
        
        for schedule_id in self.event_listeners[event_type]:
            scheduled_workflow = self.scheduled_workflows.get(schedule_id)
            
            if not scheduled_workflow or not scheduled_workflow.enabled:
                continue
            
            # Check max executions
            if (scheduled_workflow.max_executions and 
                scheduled_workflow.execution_count >= scheduled_workflow.max_executions):
                scheduled_workflow.enabled = False
                continue
            
            try:
                # Prepare execution context
                execution_context = scheduled_workflow.context.copy()
                execution_context.update({
                    "schedule_id": schedule_id,
                    "trigger_type": "event",
                    "event_type": event_type,
                    "event_data": event_data,
                    "execution_count": scheduled_workflow.execution_count + 1
                })
                
                # Execute workflow
                execution_id = await workflow_engine.execute_workflow(
                    workflow_id=scheduled_workflow.workflow_id,
                    initial_context=execution_context
                )
                
                # Update scheduled workflow
                scheduled_workflow.last_execution = datetime.now()
                scheduled_workflow.execution_count += 1
                
                osint_logger.logger.info(
                    "Event-triggered workflow executed",
                    event_type=event_type,
                    schedule_id=schedule_id,
                    execution_id=execution_id
                )
                
            except Exception as e:
                osint_logger.log_error(e, "event_triggered_workflow", {
                    "event_type": event_type,
                    "schedule_id": schedule_id
                })
    
    async def trigger_webhook(self, webhook_path: str, webhook_data: Dict[str, Any] = None):
        """Trigger workflow from webhook"""
        
        if webhook_path not in self.webhook_handlers:
            return False
        
        schedule_id = self.webhook_handlers[webhook_path]
        scheduled_workflow = self.scheduled_workflows.get(schedule_id)
        
        if not scheduled_workflow or not scheduled_workflow.enabled:
            return False
        
        # Check max executions
        if (scheduled_workflow.max_executions and 
            scheduled_workflow.execution_count >= scheduled_workflow.max_executions):
            scheduled_workflow.enabled = False
            return False
        
        try:
            # Prepare execution context
            execution_context = scheduled_workflow.context.copy()
            execution_context.update({
                "schedule_id": schedule_id,
                "trigger_type": "webhook",
                "webhook_path": webhook_path,
                "webhook_data": webhook_data or {},
                "execution_count": scheduled_workflow.execution_count + 1
            })
            
            # Execute workflow
            execution_id = await workflow_engine.execute_workflow(
                workflow_id=scheduled_workflow.workflow_id,
                initial_context=execution_context
            )
            
            # Update scheduled workflow
            scheduled_workflow.last_execution = datetime.now()
            scheduled_workflow.execution_count += 1
            
            osint_logger.logger.info(
                "Webhook-triggered workflow executed",
                webhook_path=webhook_path,
                schedule_id=schedule_id,
                execution_id=execution_id
            )
            
            return True
            
        except Exception as e:
            osint_logger.log_error(e, "webhook_triggered_workflow", {
                "webhook_path": webhook_path,
                "schedule_id": schedule_id
            })
            return False
    
    def get_scheduled_workflows(self) -> List[Dict[str, Any]]:
        """Get list of scheduled workflows"""
        
        workflows = []
        
        for schedule_id, scheduled_workflow in self.scheduled_workflows.items():
            workflows.append({
                "schedule_id": schedule_id,
                "workflow_id": scheduled_workflow.workflow_id,
                "trigger_type": scheduled_workflow.trigger_type.value,
                "schedule": scheduled_workflow.schedule,
                "event_type": scheduled_workflow.event_type,
                "webhook_path": scheduled_workflow.webhook_path,
                "file_path": scheduled_workflow.file_path,
                "enabled": scheduled_workflow.enabled,
                "last_execution": scheduled_workflow.last_execution.isoformat() if scheduled_workflow.last_execution else None,
                "next_execution": scheduled_workflow.next_execution.isoformat() if scheduled_workflow.next_execution else None,
                "execution_count": scheduled_workflow.execution_count,
                "max_executions": scheduled_workflow.max_executions
            })
        
        return workflows
    
    def enable_schedule(self, schedule_id: str) -> bool:
        """Enable a scheduled workflow"""
        
        if schedule_id in self.scheduled_workflows:
            self.scheduled_workflows[schedule_id].enabled = True
            osint_logger.logger.info(f"Schedule enabled: {schedule_id}")
            return True
        
        return False
    
    def disable_schedule(self, schedule_id: str) -> bool:
        """Disable a scheduled workflow"""
        
        if schedule_id in self.scheduled_workflows:
            self.scheduled_workflows[schedule_id].enabled = False
            osint_logger.logger.info(f"Schedule disabled: {schedule_id}")
            return True
        
        return False
    
    def get_schedule_stats(self) -> Dict[str, Any]:
        """Get scheduler statistics"""
        
        total_schedules = len(self.scheduled_workflows)
        enabled_schedules = sum(1 for sw in self.scheduled_workflows.values() if sw.enabled)
        
        trigger_types = {}
        for sw in self.scheduled_workflows.values():
            trigger_type = sw.trigger_type.value
            trigger_types[trigger_type] = trigger_types.get(trigger_type, 0) + 1
        
        total_executions = sum(sw.execution_count for sw in self.scheduled_workflows.values())
        
        return {
            "total_schedules": total_schedules,
            "enabled_schedules": enabled_schedules,
            "disabled_schedules": total_schedules - enabled_schedules,
            "trigger_types": trigger_types,
            "total_executions": total_executions,
            "event_listeners": len(self.event_listeners),
            "webhook_handlers": len(self.webhook_handlers),
            "file_watchers": len(self.file_watchers),
            "scheduler_running": self.running
        }


# Global scheduler instance
workflow_scheduler = WorkflowScheduler()
