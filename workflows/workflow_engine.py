"""
🧠 CrewAI OSINT Agent Framework - Advanced Workflow Engine

Sophisticated workflow orchestration system for complex multi-agent OSINT operations
with event-driven processing, batch capabilities, and dynamic task routing.
"""

import asyncio
import json
import uuid
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional, Callable, Union
from pathlib import Path
from dataclasses import dataclass, asdict
from enum import Enum
import sys

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from utils.logging_config import osint_logger, log_execution_time
from utils.memory_manager import memory_manager


class WorkflowStatus(Enum):
    """Workflow execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"


class TaskStatus(Enum):
    """Individual task status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"
    RETRYING = "retrying"


class TaskType(Enum):
    """Types of workflow tasks"""
    AGENT_TASK = "agent_task"
    TOOL_TASK = "tool_task"
    CONDITION = "condition"
    PARALLEL = "parallel"
    SEQUENTIAL = "sequential"
    WEBHOOK = "webhook"
    DELAY = "delay"
    CUSTOM = "custom"


@dataclass
class WorkflowTask:
    """Individual workflow task definition"""
    id: str
    name: str
    task_type: TaskType
    agent_type: Optional[str] = None
    tool_name: Optional[str] = None
    parameters: Dict[str, Any] = None
    dependencies: List[str] = None
    conditions: Dict[str, Any] = None
    retry_config: Dict[str, Any] = None
    timeout: Optional[int] = None
    priority: int = 0
    
    def __post_init__(self):
        if self.parameters is None:
            self.parameters = {}
        if self.dependencies is None:
            self.dependencies = []
        if self.conditions is None:
            self.conditions = {}
        if self.retry_config is None:
            self.retry_config = {"max_retries": 3, "delay": 1}


@dataclass
class WorkflowExecution:
    """Workflow execution state"""
    workflow_id: str
    execution_id: str
    status: WorkflowStatus
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    task_results: Dict[str, Any] = None
    error_message: Optional[str] = None
    context: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.task_results is None:
            self.task_results = {}
        if self.context is None:
            self.context = {}


@dataclass
class WorkflowDefinition:
    """Complete workflow definition"""
    id: str
    name: str
    description: str
    version: str
    tasks: List[WorkflowTask]
    triggers: Dict[str, Any] = None
    schedule: Optional[str] = None
    timeout: Optional[int] = None
    max_concurrent_executions: int = 1
    created_at: Optional[datetime] = None
    
    def __post_init__(self):
        if self.triggers is None:
            self.triggers = {}
        if self.created_at is None:
            self.created_at = datetime.now()


class WorkflowEngine:
    """Advanced workflow orchestration engine"""
    
    def __init__(self):
        self.workflows: Dict[str, WorkflowDefinition] = {}
        self.executions: Dict[str, WorkflowExecution] = {}
        self.running_executions: Dict[str, asyncio.Task] = {}
        self.event_handlers: Dict[str, List[Callable]] = {}
        self.task_registry: Dict[str, Callable] = {}
        
        self.setup_default_tasks()
        
        osint_logger.logger.info("Workflow engine initialized")
    
    def setup_default_tasks(self):
        """Setup default task handlers"""
        
        self.register_task_handler("agent_task", self._execute_agent_task)
        self.register_task_handler("tool_task", self._execute_tool_task)
        self.register_task_handler("condition", self._execute_condition)
        self.register_task_handler("parallel", self._execute_parallel)
        self.register_task_handler("sequential", self._execute_sequential)
        self.register_task_handler("webhook", self._execute_webhook)
        self.register_task_handler("delay", self._execute_delay)
    
    def register_workflow(self, workflow: WorkflowDefinition):
        """Register a workflow definition"""
        
        # Validate workflow
        self._validate_workflow(workflow)
        
        self.workflows[workflow.id] = workflow
        
        osint_logger.logger.info(
            "Workflow registered",
            workflow_id=workflow.id,
            workflow_name=workflow.name,
            task_count=len(workflow.tasks)
        )
    
    def register_task_handler(self, task_type: str, handler: Callable):
        """Register a custom task handler"""
        
        self.task_registry[task_type] = handler
        osint_logger.logger.info(f"Task handler registered: {task_type}")
    
    def register_event_handler(self, event_type: str, handler: Callable):
        """Register an event handler"""
        
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []
        
        self.event_handlers[event_type].append(handler)
        osint_logger.logger.info(f"Event handler registered: {event_type}")
    
    async def execute_workflow(self, workflow_id: str, 
                             initial_context: Dict[str, Any] = None) -> str:
        """Execute a workflow"""
        
        if workflow_id not in self.workflows:
            raise ValueError(f"Workflow {workflow_id} not found")
        
        workflow = self.workflows[workflow_id]
        
        # Check concurrent execution limit
        active_executions = sum(
            1 for exec in self.executions.values()
            if exec.workflow_id == workflow_id and exec.status == WorkflowStatus.RUNNING
        )
        
        if active_executions >= workflow.max_concurrent_executions:
            raise RuntimeError(f"Maximum concurrent executions ({workflow.max_concurrent_executions}) reached for workflow {workflow_id}")
        
        # Create execution
        execution_id = str(uuid.uuid4())
        execution = WorkflowExecution(
            workflow_id=workflow_id,
            execution_id=execution_id,
            status=WorkflowStatus.PENDING,
            created_at=datetime.now(),
            context=initial_context or {}
        )
        
        self.executions[execution_id] = execution
        
        # Start execution
        task = asyncio.create_task(self._run_workflow_execution(execution))
        self.running_executions[execution_id] = task
        
        # Emit event
        await self._emit_event("workflow_started", {
            "workflow_id": workflow_id,
            "execution_id": execution_id
        })
        
        osint_logger.logger.info(
            "Workflow execution started",
            workflow_id=workflow_id,
            execution_id=execution_id
        )
        
        return execution_id
    
    async def _run_workflow_execution(self, execution: WorkflowExecution):
        """Run a workflow execution"""
        
        workflow = self.workflows[execution.workflow_id]
        
        try:
            execution.status = WorkflowStatus.RUNNING
            execution.started_at = datetime.now()
            
            # Build task dependency graph
            task_graph = self._build_task_graph(workflow.tasks)
            
            # Execute tasks based on dependencies
            await self._execute_task_graph(execution, task_graph)
            
            execution.status = WorkflowStatus.COMPLETED
            execution.completed_at = datetime.now()
            
            await self._emit_event("workflow_completed", {
                "workflow_id": execution.workflow_id,
                "execution_id": execution.execution_id,
                "duration": (execution.completed_at - execution.started_at).total_seconds()
            })
            
        except Exception as e:
            execution.status = WorkflowStatus.FAILED
            execution.error_message = str(e)
            execution.completed_at = datetime.now()
            
            osint_logger.log_error(e, "workflow_execution", {
                "workflow_id": execution.workflow_id,
                "execution_id": execution.execution_id
            })
            
            await self._emit_event("workflow_failed", {
                "workflow_id": execution.workflow_id,
                "execution_id": execution.execution_id,
                "error": str(e)
            })
        
        finally:
            # Cleanup
            if execution.execution_id in self.running_executions:
                del self.running_executions[execution.execution_id]
    
    def _build_task_graph(self, tasks: List[WorkflowTask]) -> Dict[str, List[str]]:
        """Build task dependency graph"""
        
        graph = {}
        
        for task in tasks:
            graph[task.id] = task.dependencies.copy()
        
        return graph
    
    async def _execute_task_graph(self, execution: WorkflowExecution, 
                                task_graph: Dict[str, List[str]]):
        """Execute tasks based on dependency graph"""
        
        workflow = self.workflows[execution.workflow_id]
        task_map = {task.id: task for task in workflow.tasks}
        
        completed_tasks = set()
        running_tasks = {}
        
        while len(completed_tasks) < len(workflow.tasks):
            # Find tasks ready to execute
            ready_tasks = []
            
            for task_id, dependencies in task_graph.items():
                if (task_id not in completed_tasks and 
                    task_id not in running_tasks and
                    all(dep in completed_tasks for dep in dependencies)):
                    ready_tasks.append(task_id)
            
            if not ready_tasks and not running_tasks:
                # Deadlock - circular dependencies
                raise RuntimeError("Circular dependency detected in workflow")
            
            # Start ready tasks
            for task_id in ready_tasks:
                task = task_map[task_id]
                task_coroutine = self._execute_single_task(execution, task)
                running_tasks[task_id] = asyncio.create_task(task_coroutine)
            
            # Wait for at least one task to complete
            if running_tasks:
                done, pending = await asyncio.wait(
                    running_tasks.values(),
                    return_when=asyncio.FIRST_COMPLETED
                )
                
                # Process completed tasks
                for task_future in done:
                    # Find which task completed
                    completed_task_id = None
                    for tid, future in running_tasks.items():
                        if future == task_future:
                            completed_task_id = tid
                            break
                    
                    if completed_task_id:
                        try:
                            result = await task_future
                            execution.task_results[completed_task_id] = result
                            completed_tasks.add(completed_task_id)
                        except Exception as e:
                            # Task failed
                            execution.task_results[completed_task_id] = {"error": str(e)}
                            
                            # Check if task failure should stop workflow
                            task = task_map[completed_task_id]
                            if task.conditions.get("continue_on_failure", False):
                                completed_tasks.add(completed_task_id)
                            else:
                                raise e
                        
                        finally:
                            del running_tasks[completed_task_id]
    
    async def _execute_single_task(self, execution: WorkflowExecution, 
                                 task: WorkflowTask) -> Any:
        """Execute a single workflow task"""
        
        osint_logger.logger.info(
            "Executing task",
            task_id=task.id,
            task_name=task.name,
            task_type=task.task_type.value
        )
        
        # Check conditions
        if not self._check_task_conditions(execution, task):
            osint_logger.logger.info(f"Task {task.id} skipped due to conditions")
            return {"status": "skipped", "reason": "conditions_not_met"}
        
        # Get task handler
        handler = self.task_registry.get(task.task_type.value)
        if not handler:
            raise ValueError(f"No handler registered for task type: {task.task_type.value}")
        
        # Execute with retry logic
        max_retries = task.retry_config.get("max_retries", 3)
        retry_delay = task.retry_config.get("delay", 1)
        
        for attempt in range(max_retries + 1):
            try:
                # Apply timeout if specified
                if task.timeout:
                    result = await asyncio.wait_for(
                        handler(execution, task),
                        timeout=task.timeout
                    )
                else:
                    result = await handler(execution, task)
                
                return result
                
            except Exception as e:
                if attempt < max_retries:
                    osint_logger.logger.warning(
                        f"Task {task.id} failed, retrying in {retry_delay}s (attempt {attempt + 1}/{max_retries})"
                    )
                    await asyncio.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                else:
                    raise e
    
    def _check_task_conditions(self, execution: WorkflowExecution, 
                             task: WorkflowTask) -> bool:
        """Check if task conditions are met"""
        
        if not task.conditions:
            return True
        
        # Simple condition evaluation
        for condition_key, condition_value in task.conditions.items():
            if condition_key == "if_result":
                # Check previous task result
                dep_task_id = condition_value.get("task_id")
                expected_value = condition_value.get("equals")
                
                if dep_task_id in execution.task_results:
                    actual_value = execution.task_results[dep_task_id]
                    if actual_value != expected_value:
                        return False
            
            elif condition_key == "if_context":
                # Check context value
                context_key = condition_value.get("key")
                expected_value = condition_value.get("equals")
                
                if context_key in execution.context:
                    actual_value = execution.context[context_key]
                    if actual_value != expected_value:
                        return False
        
        return True
    
    def _validate_workflow(self, workflow: WorkflowDefinition):
        """Validate workflow definition"""
        
        task_ids = {task.id for task in workflow.tasks}
        
        # Check for duplicate task IDs
        if len(task_ids) != len(workflow.tasks):
            raise ValueError("Duplicate task IDs found in workflow")
        
        # Check dependencies
        for task in workflow.tasks:
            for dep_id in task.dependencies:
                if dep_id not in task_ids:
                    raise ValueError(f"Task {task.id} depends on non-existent task {dep_id}")
        
        # Check for circular dependencies (simplified)
        # A more sophisticated algorithm would be needed for complex cases
        visited = set()
        rec_stack = set()
        
        def has_cycle(task_id):
            if task_id in rec_stack:
                return True
            if task_id in visited:
                return False
            
            visited.add(task_id)
            rec_stack.add(task_id)
            
            task = next(t for t in workflow.tasks if t.id == task_id)
            for dep_id in task.dependencies:
                if has_cycle(dep_id):
                    return True
            
            rec_stack.remove(task_id)
            return False
        
        for task in workflow.tasks:
            if task.id not in visited:
                if has_cycle(task.id):
                    raise ValueError("Circular dependency detected in workflow")
    
    async def _emit_event(self, event_type: str, event_data: Dict[str, Any]):
        """Emit workflow event"""
        
        if event_type in self.event_handlers:
            for handler in self.event_handlers[event_type]:
                try:
                    if asyncio.iscoroutinefunction(handler):
                        await handler(event_data)
                    else:
                        handler(event_data)
                except Exception as e:
                    osint_logger.log_error(e, "event_handler", {
                        "event_type": event_type,
                        "handler": str(handler)
                    })

    # Default task handlers

    async def _execute_agent_task(self, execution: WorkflowExecution,
                                task: WorkflowTask) -> Dict[str, Any]:
        """Execute an agent task"""

        agent_type = task.agent_type
        parameters = task.parameters.copy()

        # Add context to parameters
        parameters.update(execution.context)

        try:
            if agent_type == "geopolitical":
                from agents.stateful_geo_agent import StatefulGeopoliticalAgent
                agent = StatefulGeopoliticalAgent(user_id=execution.execution_id)

                result = agent.analyze_with_context(
                    query=parameters.get("query", ""),
                    regions=parameters.get("regions", ["Global"]),
                    analysis_type=parameters.get("analysis_type", "intelligence_brief")
                )

            elif agent_type == "cti":
                from agents.stateful_cti_agent import StatefulCTIAgent
                agent = StatefulCTIAgent(user_id=execution.execution_id)

                result = agent.analyze_threat_with_context(
                    content=parameters.get("content", ""),
                    analysis_type=parameters.get("analysis_type", "ioc_extraction")
                )

            elif agent_type == "multimodal":
                from agents.multimodal_osint_agent import MultiModalOSINTAgent
                agent = MultiModalOSINTAgent(user_id=execution.execution_id)

                result = agent.analyze_media_with_context(
                    media_input=parameters.get("media_input"),
                    media_type=parameters.get("media_type", "auto"),
                    analysis_types=parameters.get("analysis_types"),
                    osint_context=parameters.get("osint_context", True)
                )

            elif agent_type == "browser":
                from agents.browser_osint_agent import BrowserOSINTAgent
                agent = BrowserOSINTAgent(user_id=execution.execution_id, headless=True)

                result = agent.investigate_target(
                    target=parameters.get("target", ""),
                    investigation_type=parameters.get("investigation_type", "comprehensive"),
                    use_stealth=parameters.get("use_stealth", True)
                )

            else:
                raise ValueError(f"Unknown agent type: {agent_type}")

            return {
                "status": "completed",
                "agent_type": agent_type,
                "result": result,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            return {
                "status": "failed",
                "agent_type": agent_type,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    async def _execute_tool_task(self, execution: WorkflowExecution,
                               task: WorkflowTask) -> Dict[str, Any]:
        """Execute a tool task"""

        tool_name = task.tool_name
        parameters = task.parameters.copy()

        try:
            if tool_name == "serper_search":
                from tools.serper_wrapper import SerperWrapper
                serper = SerperWrapper()

                result = serper.search(
                    query=parameters.get("query", ""),
                    search_type=parameters.get("search_type", "search")
                )

            elif tool_name == "crawl_url":
                from tools.crawl4ai_wrapper import Crawl4AIWrapper
                crawler = Crawl4AIWrapper()

                result = crawler.crawl_url(
                    url=parameters.get("url", ""),
                    extract_content=parameters.get("extract_content", True)
                )

            elif tool_name == "multimodal_analysis":
                from tools.multimodal_analyzer import MultiModalAnalyzer
                analyzer = MultiModalAnalyzer()

                result = analyzer.analyze_image(
                    image_path=parameters.get("image_path"),
                    analysis_types=parameters.get("analysis_types", ["metadata", "ocr"])
                )

            else:
                raise ValueError(f"Unknown tool: {tool_name}")

            return {
                "status": "completed",
                "tool_name": tool_name,
                "result": result,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            return {
                "status": "failed",
                "tool_name": tool_name,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    async def _execute_condition(self, execution: WorkflowExecution,
                                task: WorkflowTask) -> Dict[str, Any]:
        """Execute a conditional task"""

        condition = task.parameters.get("condition", {})

        # Evaluate condition
        result = self._evaluate_condition(execution, condition)

        return {
            "status": "completed",
            "condition_result": result,
            "timestamp": datetime.now().isoformat()
        }

    async def _execute_parallel(self, execution: WorkflowExecution,
                              task: WorkflowTask) -> Dict[str, Any]:
        """Execute tasks in parallel"""

        subtask_ids = task.parameters.get("subtasks", [])
        workflow = self.workflows[execution.workflow_id]
        subtasks = [t for t in workflow.tasks if t.id in subtask_ids]

        # Execute all subtasks in parallel
        results = await asyncio.gather(
            *[self._execute_single_task(execution, subtask) for subtask in subtasks],
            return_exceptions=True
        )

        # Combine results
        combined_results = {}
        for i, subtask in enumerate(subtasks):
            combined_results[subtask.id] = results[i]

        return {
            "status": "completed",
            "parallel_results": combined_results,
            "timestamp": datetime.now().isoformat()
        }

    async def _execute_sequential(self, execution: WorkflowExecution,
                                task: WorkflowTask) -> Dict[str, Any]:
        """Execute tasks sequentially"""

        subtask_ids = task.parameters.get("subtasks", [])
        workflow = self.workflows[execution.workflow_id]
        subtasks = [t for t in workflow.tasks if t.id in subtask_ids]

        # Execute subtasks one by one
        results = {}
        for subtask in subtasks:
            result = await self._execute_single_task(execution, subtask)
            results[subtask.id] = result

            # Stop on failure if configured
            if (isinstance(result, dict) and
                result.get("status") == "failed" and
                not task.parameters.get("continue_on_failure", False)):
                break

        return {
            "status": "completed",
            "sequential_results": results,
            "timestamp": datetime.now().isoformat()
        }

    async def _execute_webhook(self, execution: WorkflowExecution,
                             task: WorkflowTask) -> Dict[str, Any]:
        """Execute a webhook call"""

        import httpx

        url = task.parameters.get("url", "")
        method = task.parameters.get("method", "POST").upper()
        headers = task.parameters.get("headers", {})
        data = task.parameters.get("data", {})

        # Add execution context to data
        data.update({
            "workflow_id": execution.workflow_id,
            "execution_id": execution.execution_id,
            "context": execution.context
        })

        try:
            async with httpx.AsyncClient() as client:
                if method == "GET":
                    response = await client.get(url, headers=headers, params=data)
                elif method == "POST":
                    response = await client.post(url, headers=headers, json=data)
                elif method == "PUT":
                    response = await client.put(url, headers=headers, json=data)
                else:
                    raise ValueError(f"Unsupported HTTP method: {method}")

                response.raise_for_status()

                return {
                    "status": "completed",
                    "webhook_response": {
                        "status_code": response.status_code,
                        "response_data": response.json() if response.headers.get("content-type", "").startswith("application/json") else response.text
                    },
                    "timestamp": datetime.now().isoformat()
                }

        except Exception as e:
            return {
                "status": "failed",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    async def _execute_delay(self, execution: WorkflowExecution,
                           task: WorkflowTask) -> Dict[str, Any]:
        """Execute a delay task"""

        delay_seconds = task.parameters.get("seconds", 1)

        await asyncio.sleep(delay_seconds)

        return {
            "status": "completed",
            "delay_seconds": delay_seconds,
            "timestamp": datetime.now().isoformat()
        }

    def _evaluate_condition(self, execution: WorkflowExecution,
                          condition: Dict[str, Any]) -> bool:
        """Evaluate a condition"""

        condition_type = condition.get("type", "equals")

        if condition_type == "equals":
            left = self._resolve_value(execution, condition.get("left"))
            right = self._resolve_value(execution, condition.get("right"))
            return left == right

        elif condition_type == "not_equals":
            left = self._resolve_value(execution, condition.get("left"))
            right = self._resolve_value(execution, condition.get("right"))
            return left != right

        elif condition_type == "greater_than":
            left = self._resolve_value(execution, condition.get("left"))
            right = self._resolve_value(execution, condition.get("right"))
            return left > right

        elif condition_type == "less_than":
            left = self._resolve_value(execution, condition.get("left"))
            right = self._resolve_value(execution, condition.get("right"))
            return left < right

        elif condition_type == "contains":
            left = self._resolve_value(execution, condition.get("left"))
            right = self._resolve_value(execution, condition.get("right"))
            return right in left

        elif condition_type == "and":
            conditions = condition.get("conditions", [])
            return all(self._evaluate_condition(execution, cond) for cond in conditions)

        elif condition_type == "or":
            conditions = condition.get("conditions", [])
            return any(self._evaluate_condition(execution, cond) for cond in conditions)

        return False

    def _resolve_value(self, execution: WorkflowExecution, value_spec: Any) -> Any:
        """Resolve a value specification to actual value"""

        if isinstance(value_spec, dict):
            if "task_result" in value_spec:
                task_id = value_spec["task_result"]
                path = value_spec.get("path", [])

                if task_id in execution.task_results:
                    result = execution.task_results[task_id]

                    # Navigate path
                    for key in path:
                        if isinstance(result, dict) and key in result:
                            result = result[key]
                        else:
                            return None

                    return result

            elif "context" in value_spec:
                key = value_spec["context"]
                return execution.context.get(key)

        return value_spec

    # Workflow management methods

    def get_workflow_status(self, execution_id: str) -> Optional[WorkflowExecution]:
        """Get workflow execution status"""
        return self.executions.get(execution_id)

    def list_workflows(self) -> List[WorkflowDefinition]:
        """List all registered workflows"""
        return list(self.workflows.values())

    def list_executions(self, workflow_id: Optional[str] = None) -> List[WorkflowExecution]:
        """List workflow executions"""
        executions = list(self.executions.values())

        if workflow_id:
            executions = [e for e in executions if e.workflow_id == workflow_id]

        return executions

    async def cancel_execution(self, execution_id: str) -> bool:
        """Cancel a running workflow execution"""

        if execution_id in self.running_executions:
            task = self.running_executions[execution_id]
            task.cancel()

            if execution_id in self.executions:
                self.executions[execution_id].status = WorkflowStatus.CANCELLED
                self.executions[execution_id].completed_at = datetime.now()

            await self._emit_event("workflow_cancelled", {
                "execution_id": execution_id
            })

            return True

        return False

    def export_workflow(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """Export workflow definition"""

        if workflow_id in self.workflows:
            workflow = self.workflows[workflow_id]
            return asdict(workflow)

        return None

    def import_workflow(self, workflow_data: Dict[str, Any]) -> str:
        """Import workflow definition"""

        # Convert dict to WorkflowDefinition
        tasks = [
            WorkflowTask(**task_data)
            for task_data in workflow_data.get("tasks", [])
        ]

        workflow = WorkflowDefinition(
            id=workflow_data["id"],
            name=workflow_data["name"],
            description=workflow_data["description"],
            version=workflow_data["version"],
            tasks=tasks,
            triggers=workflow_data.get("triggers", {}),
            schedule=workflow_data.get("schedule"),
            timeout=workflow_data.get("timeout"),
            max_concurrent_executions=workflow_data.get("max_concurrent_executions", 1)
        )

        self.register_workflow(workflow)
        return workflow.id


# Global workflow engine instance
workflow_engine = WorkflowEngine()
