"""
🧠 CrewAI OSINT Agent Framework - Workflow Builder

Intuitive workflow builder with templates and visual workflow construction
for complex multi-agent OSINT operations.
"""

import json
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path
import sys

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from workflows.workflow_engine import (
    WorkflowDefinition, WorkflowTask, TaskType, workflow_engine
)
from utils.logging_config import osint_logger


class WorkflowBuilder:
    """Fluent interface for building workflows"""
    
    def __init__(self, name: str, description: str = ""):
        self.workflow_id = str(uuid.uuid4())
        self.name = name
        self.description = description
        self.version = "1.0.0"
        self.tasks: List[WorkflowTask] = []
        self.triggers: Dict[str, Any] = {}
        self.schedule: Optional[str] = None
        self.timeout: Optional[int] = None
        self.max_concurrent_executions = 1
        
        osint_logger.logger.info(f"Workflow builder created: {name}")
    
    def add_agent_task(self, task_id: str, name: str, agent_type: str, 
                      parameters: Dict[str, Any] = None, 
                      dependencies: List[str] = None) -> 'WorkflowBuilder':
        """Add an agent task to the workflow"""
        
        task = WorkflowTask(
            id=task_id,
            name=name,
            task_type=TaskType.AGENT_TASK,
            agent_type=agent_type,
            parameters=parameters or {},
            dependencies=dependencies or []
        )
        
        self.tasks.append(task)
        return self
    
    def add_tool_task(self, task_id: str, name: str, tool_name: str,
                     parameters: Dict[str, Any] = None,
                     dependencies: List[str] = None) -> 'WorkflowBuilder':
        """Add a tool task to the workflow"""
        
        task = WorkflowTask(
            id=task_id,
            name=name,
            task_type=TaskType.TOOL_TASK,
            tool_name=tool_name,
            parameters=parameters or {},
            dependencies=dependencies or []
        )
        
        self.tasks.append(task)
        return self
    
    def add_condition_task(self, task_id: str, name: str, condition: Dict[str, Any],
                          dependencies: List[str] = None) -> 'WorkflowBuilder':
        """Add a conditional task to the workflow"""
        
        task = WorkflowTask(
            id=task_id,
            name=name,
            task_type=TaskType.CONDITION,
            parameters={"condition": condition},
            dependencies=dependencies or []
        )
        
        self.tasks.append(task)
        return self
    
    def add_parallel_task(self, task_id: str, name: str, subtask_ids: List[str],
                         dependencies: List[str] = None) -> 'WorkflowBuilder':
        """Add a parallel execution task"""
        
        task = WorkflowTask(
            id=task_id,
            name=name,
            task_type=TaskType.PARALLEL,
            parameters={"subtasks": subtask_ids},
            dependencies=dependencies or []
        )
        
        self.tasks.append(task)
        return self
    
    def add_delay_task(self, task_id: str, name: str, seconds: int,
                      dependencies: List[str] = None) -> 'WorkflowBuilder':
        """Add a delay task"""
        
        task = WorkflowTask(
            id=task_id,
            name=name,
            task_type=TaskType.DELAY,
            parameters={"seconds": seconds},
            dependencies=dependencies or []
        )
        
        self.tasks.append(task)
        return self
    
    def add_webhook_task(self, task_id: str, name: str, url: str, method: str = "POST",
                        headers: Dict[str, str] = None, data: Dict[str, Any] = None,
                        dependencies: List[str] = None) -> 'WorkflowBuilder':
        """Add a webhook task"""
        
        task = WorkflowTask(
            id=task_id,
            name=name,
            task_type=TaskType.WEBHOOK,
            parameters={
                "url": url,
                "method": method,
                "headers": headers or {},
                "data": data or {}
            },
            dependencies=dependencies or []
        )
        
        self.tasks.append(task)
        return self
    
    def set_triggers(self, triggers: Dict[str, Any]) -> 'WorkflowBuilder':
        """Set workflow triggers"""
        self.triggers = triggers
        return self
    
    def set_schedule(self, schedule: str) -> 'WorkflowBuilder':
        """Set workflow schedule (cron format)"""
        self.schedule = schedule
        return self
    
    def set_timeout(self, timeout: int) -> 'WorkflowBuilder':
        """Set workflow timeout in seconds"""
        self.timeout = timeout
        return self
    
    def set_max_concurrent_executions(self, max_executions: int) -> 'WorkflowBuilder':
        """Set maximum concurrent executions"""
        self.max_concurrent_executions = max_executions
        return self
    
    def build(self) -> WorkflowDefinition:
        """Build the workflow definition"""
        
        workflow = WorkflowDefinition(
            id=self.workflow_id,
            name=self.name,
            description=self.description,
            version=self.version,
            tasks=self.tasks,
            triggers=self.triggers,
            schedule=self.schedule,
            timeout=self.timeout,
            max_concurrent_executions=self.max_concurrent_executions
        )
        
        osint_logger.logger.info(f"Workflow built: {self.name} with {len(self.tasks)} tasks")
        return workflow
    
    def register(self) -> str:
        """Build and register the workflow"""
        
        workflow = self.build()
        workflow_engine.register_workflow(workflow)
        return workflow.id


class WorkflowTemplates:
    """Pre-built workflow templates for common OSINT scenarios"""
    
    @staticmethod
    def comprehensive_target_investigation(target: str) -> WorkflowBuilder:
        """Create a comprehensive target investigation workflow"""
        
        builder = WorkflowBuilder(
            name=f"Comprehensive Investigation: {target}",
            description=f"Complete OSINT investigation of target: {target}"
        )
        
        # Initial search and reconnaissance
        builder.add_tool_task(
            task_id="search_general",
            name="General Web Search",
            tool_name="serper_search",
            parameters={
                "query": target,
                "search_type": "search"
            }
        )
        
        builder.add_tool_task(
            task_id="search_news",
            name="News Search",
            tool_name="serper_search",
            parameters={
                "query": target,
                "search_type": "news"
            }
        )
        
        # Parallel agent analysis
        builder.add_agent_task(
            task_id="geo_analysis",
            name="Geopolitical Analysis",
            agent_type="geopolitical",
            parameters={
                "query": f"Geopolitical analysis of {target}",
                "regions": ["Global"],
                "analysis_type": "intelligence_brief"
            },
            dependencies=["search_general", "search_news"]
        )
        
        builder.add_agent_task(
            task_id="cti_analysis",
            name="Threat Intelligence Analysis",
            agent_type="cti",
            parameters={
                "content": f"Threat intelligence analysis for {target}",
                "analysis_type": "ioc_extraction"
            },
            dependencies=["search_general", "search_news"]
        )
        
        # Browser investigation
        builder.add_agent_task(
            task_id="browser_investigation",
            name="Browser Investigation",
            agent_type="browser",
            parameters={
                "target": target,
                "investigation_type": "comprehensive",
                "use_stealth": True
            },
            dependencies=["geo_analysis", "cti_analysis"]
        )
        
        # Final correlation and reporting
        builder.add_condition_task(
            task_id="check_findings",
            name="Check Investigation Findings",
            condition={
                "type": "and",
                "conditions": [
                    {
                        "type": "not_equals",
                        "left": {"task_result": "geo_analysis", "path": ["status"]},
                        "right": "failed"
                    },
                    {
                        "type": "not_equals",
                        "left": {"task_result": "cti_analysis", "path": ["status"]},
                        "right": "failed"
                    }
                ]
            },
            dependencies=["browser_investigation"]
        )
        
        return builder
    
    @staticmethod
    def threat_monitoring_pipeline() -> WorkflowBuilder:
        """Create a threat monitoring pipeline workflow"""
        
        builder = WorkflowBuilder(
            name="Threat Monitoring Pipeline",
            description="Continuous threat monitoring and analysis pipeline"
        )
        
        # Search for threat indicators
        builder.add_tool_task(
            task_id="threat_search",
            name="Threat Intelligence Search",
            tool_name="serper_search",
            parameters={
                "query": "cybersecurity threats malware ransomware",
                "search_type": "news"
            }
        )
        
        # CTI analysis
        builder.add_agent_task(
            task_id="cti_monitoring",
            name="CTI Monitoring Analysis",
            agent_type="cti",
            parameters={
                "content": "Latest cybersecurity threats and indicators",
                "analysis_type": "threat_actor_tracking"
            },
            dependencies=["threat_search"]
        )
        
        # Condition to check for high-priority threats
        builder.add_condition_task(
            task_id="check_threat_level",
            name="Check Threat Level",
            condition={
                "type": "contains",
                "left": {"task_result": "cti_monitoring", "path": ["result", "threat_indicators"]},
                "right": "high"
            },
            dependencies=["cti_monitoring"]
        )
        
        # Alert webhook for high-priority threats
        builder.add_webhook_task(
            task_id="threat_alert",
            name="Send Threat Alert",
            url="https://hooks.slack.com/services/YOUR/WEBHOOK/URL",
            method="POST",
            data={
                "text": "High-priority threat detected in monitoring pipeline",
                "channel": "#security-alerts"
            },
            dependencies=["check_threat_level"]
        )
        
        return builder
    
    @staticmethod
    def social_media_monitoring(target: str, platforms: List[str] = None) -> WorkflowBuilder:
        """Create a social media monitoring workflow"""
        
        if platforms is None:
            platforms = ["twitter", "linkedin", "facebook"]
        
        builder = WorkflowBuilder(
            name=f"Social Media Monitoring: {target}",
            description=f"Monitor social media presence for {target}"
        )
        
        # Search each platform
        for i, platform in enumerate(platforms):
            builder.add_tool_task(
                task_id=f"search_{platform}",
                name=f"Search {platform.title()}",
                tool_name="serper_search",
                parameters={
                    "query": f"site:{platform}.com {target}",
                    "search_type": "search"
                }
            )
        
        # Browser investigation for found profiles
        builder.add_agent_task(
            task_id="browser_social_analysis",
            name="Browser Social Media Analysis",
            agent_type="browser",
            parameters={
                "target": target,
                "investigation_type": "social_media",
                "use_stealth": True
            },
            dependencies=[f"search_{platform}" for platform in platforms]
        )
        
        # Geopolitical context analysis
        builder.add_agent_task(
            task_id="social_geo_context",
            name="Social Media Geopolitical Context",
            agent_type="geopolitical",
            parameters={
                "query": f"Social media analysis and geopolitical context for {target}",
                "regions": ["Global"],
                "analysis_type": "intelligence_brief"
            },
            dependencies=["browser_social_analysis"]
        )
        
        return builder
    
    @staticmethod
    def batch_domain_analysis(domains: List[str]) -> WorkflowBuilder:
        """Create a batch domain analysis workflow"""
        
        builder = WorkflowBuilder(
            name="Batch Domain Analysis",
            description=f"Analyze {len(domains)} domains in parallel"
        )
        
        # Create parallel analysis tasks for each domain
        domain_task_ids = []
        
        for i, domain in enumerate(domains):
            task_id = f"analyze_domain_{i}"
            domain_task_ids.append(task_id)
            
            builder.add_agent_task(
                task_id=task_id,
                name=f"Analyze {domain}",
                agent_type="browser",
                parameters={
                    "target": domain,
                    "investigation_type": "technical",
                    "use_stealth": True
                }
            )
        
        # Parallel execution of all domain analyses
        builder.add_parallel_task(
            task_id="parallel_domain_analysis",
            name="Parallel Domain Analysis",
            subtask_ids=domain_task_ids
        )
        
        # Aggregate results
        builder.add_agent_task(
            task_id="aggregate_results",
            name="Aggregate Domain Analysis Results",
            agent_type="cti",
            parameters={
                "content": "Aggregate and correlate domain analysis results",
                "analysis_type": "campaign_analysis"
            },
            dependencies=["parallel_domain_analysis"]
        )
        
        return builder
    
    @staticmethod
    def incident_response_workflow(incident_type: str, indicators: List[str]) -> WorkflowBuilder:
        """Create an incident response workflow"""
        
        builder = WorkflowBuilder(
            name=f"Incident Response: {incident_type}",
            description=f"Automated incident response for {incident_type}"
        )
        
        # Initial threat assessment
        builder.add_agent_task(
            task_id="initial_assessment",
            name="Initial Threat Assessment",
            agent_type="cti",
            parameters={
                "content": f"Incident: {incident_type}. Indicators: {', '.join(indicators)}",
                "analysis_type": "ioc_extraction"
            }
        )
        
        # Parallel IOC analysis
        ioc_task_ids = []
        for i, indicator in enumerate(indicators):
            task_id = f"analyze_ioc_{i}"
            ioc_task_ids.append(task_id)
            
            builder.add_tool_task(
                task_id=task_id,
                name=f"Analyze IOC: {indicator}",
                tool_name="serper_search",
                parameters={
                    "query": f"threat intelligence {indicator}",
                    "search_type": "search"
                },
                dependencies=["initial_assessment"]
            )
        
        # Threat actor correlation
        builder.add_agent_task(
            task_id="threat_actor_correlation",
            name="Threat Actor Correlation",
            agent_type="cti",
            parameters={
                "content": f"Correlate threat actors for incident: {incident_type}",
                "analysis_type": "threat_actor_tracking"
            },
            dependencies=ioc_task_ids
        )
        
        # Geopolitical context
        builder.add_agent_task(
            task_id="geopolitical_context",
            name="Geopolitical Context Analysis",
            agent_type="geopolitical",
            parameters={
                "query": f"Geopolitical context for {incident_type} incident",
                "regions": ["Global"],
                "analysis_type": "intelligence_brief"
            },
            dependencies=["threat_actor_correlation"]
        )
        
        # Final incident report
        builder.add_webhook_task(
            task_id="incident_report",
            name="Generate Incident Report",
            url="https://your-incident-management-system.com/api/reports",
            method="POST",
            data={
                "incident_type": incident_type,
                "indicators": indicators,
                "status": "analyzed"
            },
            dependencies=["geopolitical_context"]
        )
        
        return builder
