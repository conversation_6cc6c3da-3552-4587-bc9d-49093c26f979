"""
🧠 CrewAI OSINT Agent Framework - Multi-Modal Analysis

Advanced image and video analysis capabilities for comprehensive OSINT operations.
Includes OCR, face detection, object recognition, and metadata extraction.
"""

import os
import io
import json
import base64
from datetime import datetime
from typing import Dict, List, Any, Optional, Union, Tuple
from pathlib import Path
import hashlib
import tempfile

# Core image processing
try:
    import cv2
    import numpy as np
    from PIL import Image, ExifTags
    OPENCV_AVAILABLE = True
except ImportError:
    OPENCV_AVAILABLE = False

# OCR capabilities
try:
    import pytesseract
    TESSERACT_AVAILABLE = True
except ImportError:
    TESSERACT_AVAILABLE = False

try:
    import easyocr
    EASYOCR_AVAILABLE = True
except ImportError:
    EASYOCR_AVAILABLE = False

# Face recognition
try:
    import face_recognition
    FACE_RECOGNITION_AVAILABLE = True
except ImportError:
    FACE_RECOGNITION_AVAILABLE = False

# Add project root to path
import sys
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from utils.logging_config import osint_logger, log_execution_time


class MultiModalAnalyzer:
    """Comprehensive multi-modal analysis for images and videos"""
    
    def __init__(self):
        self.setup_analyzers()
        self.supported_image_formats = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
        self.supported_video_formats = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv']
        
        osint_logger.logger.info("Multi-modal analyzer initialized")
    
    def setup_analyzers(self):
        """Setup various analysis components"""
        
        # Initialize EasyOCR if available
        if EASYOCR_AVAILABLE:
            try:
                self.ocr_reader = easyocr.Reader(['en'])
                osint_logger.logger.info("EasyOCR initialized")
            except Exception as e:
                osint_logger.logger.warning(f"Failed to initialize EasyOCR: {e}")
                self.ocr_reader = None
        else:
            self.ocr_reader = None
        
        # Check Tesseract availability
        if TESSERACT_AVAILABLE:
            try:
                # Test Tesseract
                pytesseract.get_tesseract_version()
                self.tesseract_available = True
                osint_logger.logger.info("Tesseract OCR available")
            except Exception as e:
                osint_logger.logger.warning(f"Tesseract not available: {e}")
                self.tesseract_available = False
        else:
            self.tesseract_available = False
    
    @log_execution_time("multimodal_analysis")
    def analyze_image(self, image_path: Union[str, Path, bytes], 
                     analysis_types: List[str] = None) -> Dict[str, Any]:
        """Comprehensive image analysis"""
        
        if analysis_types is None:
            analysis_types = ["metadata", "ocr", "faces", "objects", "forensics"]
        
        result = {
            "analysis_timestamp": datetime.now().isoformat(),
            "analysis_types": analysis_types,
            "image_info": {},
            "metadata": {},
            "text_extraction": {},
            "face_analysis": {},
            "object_detection": {},
            "forensic_analysis": {},
            "security_indicators": []
        }
        
        try:
            # Load image
            image, image_info = self._load_image(image_path)
            result["image_info"] = image_info
            
            if image is None:
                result["error"] = "Failed to load image"
                return result
            
            # Perform requested analyses
            if "metadata" in analysis_types:
                result["metadata"] = self._extract_metadata(image_path, image)
            
            if "ocr" in analysis_types:
                result["text_extraction"] = self._extract_text(image)
            
            if "faces" in analysis_types:
                result["face_analysis"] = self._analyze_faces(image)
            
            if "objects" in analysis_types:
                result["object_detection"] = self._detect_objects(image)
            
            if "forensics" in analysis_types:
                result["forensic_analysis"] = self._forensic_analysis(image, image_path)
            
            # Security indicators
            result["security_indicators"] = self._identify_security_indicators(result)
            
            osint_logger.logger.info(
                "Image analysis completed",
                analysis_types=analysis_types,
                faces_detected=len(result["face_analysis"].get("faces", [])),
                text_found=bool(result["text_extraction"].get("text", "").strip())
            )
            
        except Exception as e:
            osint_logger.log_error(e, "image_analysis")
            result["error"] = str(e)
        
        return result
    
    @log_execution_time("video_analysis")
    def analyze_video(self, video_path: Union[str, Path], 
                     analysis_types: List[str] = None,
                     frame_interval: int = 30) -> Dict[str, Any]:
        """Comprehensive video analysis"""
        
        if analysis_types is None:
            analysis_types = ["metadata", "frames", "audio", "motion"]
        
        result = {
            "analysis_timestamp": datetime.now().isoformat(),
            "analysis_types": analysis_types,
            "video_info": {},
            "metadata": {},
            "frame_analysis": [],
            "audio_analysis": {},
            "motion_analysis": {},
            "summary": {}
        }
        
        if not OPENCV_AVAILABLE:
            result["error"] = "OpenCV not available for video analysis"
            return result
        
        try:
            # Load video
            cap = cv2.VideoCapture(str(video_path))
            
            if not cap.isOpened():
                result["error"] = "Failed to open video file"
                return result
            
            # Get video info
            result["video_info"] = self._get_video_info(cap)
            
            # Analyze frames
            if "frames" in analysis_types:
                result["frame_analysis"] = self._analyze_video_frames(
                    cap, frame_interval
                )
            
            # Metadata extraction
            if "metadata" in analysis_types:
                result["metadata"] = self._extract_video_metadata(video_path)
            
            # Motion analysis
            if "motion" in analysis_types:
                result["motion_analysis"] = self._analyze_motion(cap)
            
            # Generate summary
            result["summary"] = self._generate_video_summary(result)
            
            cap.release()
            
            osint_logger.logger.info(
                "Video analysis completed",
                duration=result["video_info"].get("duration_seconds", 0),
                frames_analyzed=len(result["frame_analysis"])
            )
            
        except Exception as e:
            osint_logger.log_error(e, "video_analysis")
            result["error"] = str(e)
        
        return result
    
    def _load_image(self, image_input: Union[str, Path, bytes]) -> Tuple[Optional[np.ndarray], Dict[str, Any]]:
        """Load image from various input types"""
        
        image_info = {}
        
        try:
            if isinstance(image_input, bytes):
                # Load from bytes
                nparr = np.frombuffer(image_input, np.uint8)
                image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
                image_info = {
                    "source": "bytes",
                    "size_bytes": len(image_input)
                }
            else:
                # Load from file path
                image_path = Path(image_input)
                if not image_path.exists():
                    return None, {"error": "File not found"}
                
                image = cv2.imread(str(image_path))
                image_info = {
                    "source": "file",
                    "path": str(image_path),
                    "size_bytes": image_path.stat().st_size,
                    "format": image_path.suffix.lower()
                }
            
            if image is not None:
                height, width = image.shape[:2]
                image_info.update({
                    "width": width,
                    "height": height,
                    "channels": image.shape[2] if len(image.shape) > 2 else 1,
                    "total_pixels": width * height
                })
            
            return image, image_info
            
        except Exception as e:
            return None, {"error": str(e)}
    
    def _extract_metadata(self, image_path: Union[str, Path, bytes], 
                         image: np.ndarray) -> Dict[str, Any]:
        """Extract image metadata including EXIF data"""
        
        metadata = {
            "exif_data": {},
            "file_info": {},
            "image_properties": {}
        }
        
        try:
            # EXIF data extraction (only for file paths)
            if not isinstance(image_path, bytes):
                try:
                    pil_image = Image.open(image_path)
                    exif_dict = pil_image._getexif()
                    
                    if exif_dict:
                        for tag_id, value in exif_dict.items():
                            tag = ExifTags.TAGS.get(tag_id, tag_id)
                            metadata["exif_data"][tag] = str(value)
                except Exception as e:
                    metadata["exif_data"]["error"] = str(e)
            
            # Image properties
            if image is not None:
                metadata["image_properties"] = {
                    "mean_brightness": float(np.mean(cv2.cvtColor(image, cv2.COLOR_BGR2GRAY))),
                    "color_channels": image.shape[2] if len(image.shape) > 2 else 1,
                    "aspect_ratio": image.shape[1] / image.shape[0]
                }
                
                # Color analysis
                if len(image.shape) == 3:
                    b, g, r = cv2.split(image)
                    metadata["image_properties"]["color_means"] = {
                        "red": float(np.mean(r)),
                        "green": float(np.mean(g)),
                        "blue": float(np.mean(b))
                    }
        
        except Exception as e:
            metadata["error"] = str(e)
        
        return metadata
    
    def _extract_text(self, image: np.ndarray) -> Dict[str, Any]:
        """Extract text using OCR"""
        
        text_result = {
            "text": "",
            "confidence": 0.0,
            "method": "none",
            "bounding_boxes": [],
            "languages_detected": []
        }
        
        try:
            # Try EasyOCR first (better for multiple languages)
            if self.ocr_reader:
                try:
                    results = self.ocr_reader.readtext(image)
                    
                    extracted_text = []
                    total_confidence = 0
                    bounding_boxes = []
                    
                    for (bbox, text, confidence) in results:
                        if confidence > 0.5:  # Filter low confidence
                            extracted_text.append(text)
                            total_confidence += confidence
                            bounding_boxes.append({
                                "text": text,
                                "bbox": bbox,
                                "confidence": confidence
                            })
                    
                    if extracted_text:
                        text_result.update({
                            "text": " ".join(extracted_text),
                            "confidence": total_confidence / len(extracted_text),
                            "method": "easyocr",
                            "bounding_boxes": bounding_boxes
                        })
                        
                except Exception as e:
                    osint_logger.logger.warning(f"EasyOCR failed: {e}")
            
            # Fallback to Tesseract
            if not text_result["text"] and self.tesseract_available:
                try:
                    # Convert to PIL Image for Tesseract
                    pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
                    
                    # Extract text
                    text = pytesseract.image_to_string(pil_image)
                    
                    # Get confidence data
                    data = pytesseract.image_to_data(pil_image, output_type=pytesseract.Output.DICT)
                    
                    if text.strip():
                        # Calculate average confidence
                        confidences = [int(conf) for conf in data['conf'] if int(conf) > 0]
                        avg_confidence = sum(confidences) / len(confidences) if confidences else 0
                        
                        text_result.update({
                            "text": text.strip(),
                            "confidence": avg_confidence / 100.0,
                            "method": "tesseract"
                        })
                        
                except Exception as e:
                    osint_logger.logger.warning(f"Tesseract failed: {e}")
        
        except Exception as e:
            text_result["error"] = str(e)
        
        return text_result
    
    def _analyze_faces(self, image: np.ndarray) -> Dict[str, Any]:
        """Analyze faces in the image"""
        
        face_result = {
            "faces": [],
            "face_count": 0,
            "method": "none"
        }
        
        if not FACE_RECOGNITION_AVAILABLE:
            face_result["error"] = "Face recognition library not available"
            return face_result
        
        try:
            # Convert BGR to RGB for face_recognition
            rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # Find face locations
            face_locations = face_recognition.face_locations(rgb_image)
            
            if face_locations:
                face_result["face_count"] = len(face_locations)
                face_result["method"] = "face_recognition"
                
                # Get face encodings
                face_encodings = face_recognition.face_encodings(rgb_image, face_locations)
                
                for i, (location, encoding) in enumerate(zip(face_locations, face_encodings)):
                    top, right, bottom, left = location
                    
                    face_info = {
                        "face_id": i,
                        "location": {
                            "top": top,
                            "right": right,
                            "bottom": bottom,
                            "left": left
                        },
                        "size": {
                            "width": right - left,
                            "height": bottom - top
                        },
                        "encoding_hash": hashlib.md5(encoding.tobytes()).hexdigest()
                    }
                    
                    face_result["faces"].append(face_info)
        
        except Exception as e:
            face_result["error"] = str(e)
        
        return face_result
    
    def _detect_objects(self, image: np.ndarray) -> Dict[str, Any]:
        """Detect objects in the image using OpenCV"""
        
        object_result = {
            "objects": [],
            "method": "opencv_basic",
            "features": {}
        }
        
        if not OPENCV_AVAILABLE:
            object_result["error"] = "OpenCV not available"
            return object_result
        
        try:
            # Basic feature detection
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Edge detection
            edges = cv2.Canny(gray, 50, 150)
            edge_count = np.count_nonzero(edges)
            
            # Corner detection
            corners = cv2.goodFeaturesToTrack(gray, maxCorners=100, qualityLevel=0.01, minDistance=10)
            corner_count = len(corners) if corners is not None else 0
            
            # Contour detection
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            object_result["features"] = {
                "edge_pixels": int(edge_count),
                "corners_detected": corner_count,
                "contours_found": len(contours),
                "complexity_score": (edge_count + corner_count * 10) / (image.shape[0] * image.shape[1])
            }
            
            # Analyze significant contours
            significant_contours = []
            for contour in contours:
                area = cv2.contourArea(contour)
                if area > 100:  # Filter small contours
                    x, y, w, h = cv2.boundingRect(contour)
                    significant_contours.append({
                        "area": float(area),
                        "bounding_box": {"x": int(x), "y": int(y), "width": int(w), "height": int(h)},
                        "aspect_ratio": float(w / h) if h > 0 else 0
                    })
            
            object_result["objects"] = significant_contours[:20]  # Limit to top 20
        
        except Exception as e:
            object_result["error"] = str(e)

        return object_result

    def _forensic_analysis(self, image: np.ndarray, image_path: Union[str, Path, bytes]) -> Dict[str, Any]:
        """Perform forensic analysis on the image"""

        forensic_result = {
            "hash_analysis": {},
            "modification_indicators": {},
            "compression_analysis": {},
            "noise_analysis": {}
        }

        try:
            # Hash analysis
            if not isinstance(image_path, bytes):
                with open(image_path, 'rb') as f:
                    file_data = f.read()
                    forensic_result["hash_analysis"] = {
                        "md5": hashlib.md5(file_data).hexdigest(),
                        "sha256": hashlib.sha256(file_data).hexdigest()
                    }

            # Image hash (perceptual)
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            resized = cv2.resize(gray, (8, 8))
            avg = resized.mean()
            binary = resized > avg
            image_hash = ''.join(['1' if pixel else '0' for pixel in binary.flatten()])
            forensic_result["hash_analysis"]["perceptual_hash"] = image_hash

            # Noise analysis
            noise_level = np.std(cv2.Laplacian(gray, cv2.CV_64F))
            forensic_result["noise_analysis"] = {
                "noise_level": float(noise_level),
                "quality_assessment": "high" if noise_level < 100 else "medium" if noise_level < 500 else "low"
            }

            # Compression artifacts detection
            # Look for JPEG compression artifacts
            if len(image.shape) == 3:
                # Convert to YUV color space
                yuv = cv2.cvtColor(image, cv2.COLOR_BGR2YUV)
                y_channel = yuv[:, :, 0]

                # Detect blocking artifacts (8x8 blocks typical in JPEG)
                block_variance = []
                for i in range(0, y_channel.shape[0] - 8, 8):
                    for j in range(0, y_channel.shape[1] - 8, 8):
                        block = y_channel[i:i+8, j:j+8]
                        block_variance.append(np.var(block))

                if block_variance:
                    forensic_result["compression_analysis"] = {
                        "block_variance_mean": float(np.mean(block_variance)),
                        "compression_artifacts": "detected" if np.mean(block_variance) < 100 else "minimal"
                    }

        except Exception as e:
            forensic_result["error"] = str(e)

        return forensic_result

    def _identify_security_indicators(self, analysis_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Identify potential security indicators from analysis"""

        indicators = []

        try:
            # Check for suspicious text patterns
            text = analysis_result.get("text_extraction", {}).get("text", "")
            if text:
                suspicious_patterns = [
                    "password", "login", "credential", "api_key", "secret",
                    "token", "private", "confidential", "classified"
                ]

                for pattern in suspicious_patterns:
                    if pattern.lower() in text.lower():
                        indicators.append({
                            "type": "sensitive_text",
                            "pattern": pattern,
                            "confidence": 0.7,
                            "description": f"Potentially sensitive text pattern: {pattern}"
                        })

            # Check for multiple faces (potential surveillance)
            face_count = analysis_result.get("face_analysis", {}).get("face_count", 0)
            if face_count > 3:
                indicators.append({
                    "type": "multiple_faces",
                    "count": face_count,
                    "confidence": 0.6,
                    "description": f"Multiple faces detected ({face_count}), potential surveillance image"
                })

            # Check for high complexity (potential steganography)
            complexity = analysis_result.get("object_detection", {}).get("features", {}).get("complexity_score", 0)
            if complexity > 0.1:
                indicators.append({
                    "type": "high_complexity",
                    "score": complexity,
                    "confidence": 0.4,
                    "description": "High image complexity, potential steganography"
                })

            # Check for modification indicators
            noise_level = analysis_result.get("forensic_analysis", {}).get("noise_analysis", {}).get("noise_level", 0)
            if noise_level > 1000:
                indicators.append({
                    "type": "high_noise",
                    "level": noise_level,
                    "confidence": 0.5,
                    "description": "High noise level, potential image manipulation"
                })

        except Exception as e:
            osint_logger.logger.warning(f"Error identifying security indicators: {e}")

        return indicators

    def _get_video_info(self, cap: cv2.VideoCapture) -> Dict[str, Any]:
        """Get basic video information"""

        return {
            "frame_count": int(cap.get(cv2.CAP_PROP_FRAME_COUNT)),
            "fps": cap.get(cv2.CAP_PROP_FPS),
            "width": int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)),
            "height": int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT)),
            "duration_seconds": int(cap.get(cv2.CAP_PROP_FRAME_COUNT)) / cap.get(cv2.CAP_PROP_FPS) if cap.get(cv2.CAP_PROP_FPS) > 0 else 0
        }

    def _analyze_video_frames(self, cap: cv2.VideoCapture, frame_interval: int) -> List[Dict[str, Any]]:
        """Analyze video frames at specified intervals"""

        frame_analyses = []
        frame_count = 0

        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    break

                if frame_count % frame_interval == 0:
                    # Analyze this frame
                    frame_analysis = {
                        "frame_number": frame_count,
                        "timestamp": frame_count / cap.get(cv2.CAP_PROP_FPS),
                        "analysis": {}
                    }

                    # Quick analysis of the frame
                    try:
                        # Face detection
                        if FACE_RECOGNITION_AVAILABLE:
                            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                            face_locations = face_recognition.face_locations(rgb_frame)
                            frame_analysis["analysis"]["faces"] = len(face_locations)

                        # Text extraction (simplified)
                        if self.ocr_reader and frame_count % (frame_interval * 5) == 0:  # Less frequent OCR
                            try:
                                results = self.ocr_reader.readtext(frame)
                                text = " ".join([text for (_, text, conf) in results if conf > 0.7])
                                frame_analysis["analysis"]["text"] = text[:100]  # Truncate
                            except:
                                pass

                        # Motion indicators (basic)
                        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                        edges = cv2.Canny(gray, 50, 150)
                        frame_analysis["analysis"]["edge_density"] = np.count_nonzero(edges) / (frame.shape[0] * frame.shape[1])

                    except Exception as e:
                        frame_analysis["analysis"]["error"] = str(e)

                    frame_analyses.append(frame_analysis)

                    # Limit number of analyzed frames
                    if len(frame_analyses) >= 50:
                        break

                frame_count += 1

        except Exception as e:
            osint_logger.logger.warning(f"Error analyzing video frames: {e}")

        return frame_analyses

    def _extract_video_metadata(self, video_path: Union[str, Path]) -> Dict[str, Any]:
        """Extract video metadata"""

        metadata = {
            "file_info": {},
            "technical_details": {}
        }

        try:
            video_path = Path(video_path)

            metadata["file_info"] = {
                "filename": video_path.name,
                "size_bytes": video_path.stat().st_size,
                "format": video_path.suffix.lower(),
                "created": datetime.fromtimestamp(video_path.stat().st_ctime).isoformat(),
                "modified": datetime.fromtimestamp(video_path.stat().st_mtime).isoformat()
            }

            # Calculate file hash
            with open(video_path, 'rb') as f:
                file_hash = hashlib.md5()
                for chunk in iter(lambda: f.read(4096), b""):
                    file_hash.update(chunk)
                metadata["file_info"]["md5_hash"] = file_hash.hexdigest()

        except Exception as e:
            metadata["error"] = str(e)

        return metadata

    def _analyze_motion(self, cap: cv2.VideoCapture) -> Dict[str, Any]:
        """Analyze motion in video"""

        motion_result = {
            "motion_detected": False,
            "motion_intensity": 0.0,
            "motion_frames": []
        }

        try:
            # Read first frame
            ret, prev_frame = cap.read()
            if not ret:
                return motion_result

            prev_gray = cv2.cvtColor(prev_frame, cv2.COLOR_BGR2GRAY)
            frame_count = 0
            motion_scores = []

            while True:
                ret, frame = cap.read()
                if not ret:
                    break

                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

                # Calculate frame difference
                diff = cv2.absdiff(prev_gray, gray)
                motion_score = np.mean(diff)
                motion_scores.append(motion_score)

                # Detect significant motion
                if motion_score > 20:  # Threshold for motion detection
                    motion_result["motion_frames"].append({
                        "frame": frame_count,
                        "timestamp": frame_count / cap.get(cv2.CAP_PROP_FPS),
                        "intensity": float(motion_score)
                    })

                prev_gray = gray
                frame_count += 1

                # Limit analysis to avoid long processing
                if frame_count > 1000:
                    break

            if motion_scores:
                motion_result["motion_detected"] = max(motion_scores) > 20
                motion_result["motion_intensity"] = float(np.mean(motion_scores))

        except Exception as e:
            motion_result["error"] = str(e)

        return motion_result

    def _generate_video_summary(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """Generate summary of video analysis"""

        summary = {
            "total_frames_analyzed": len(analysis_result.get("frame_analysis", [])),
            "faces_detected": False,
            "text_found": False,
            "motion_detected": analysis_result.get("motion_analysis", {}).get("motion_detected", False),
            "key_findings": []
        }

        try:
            # Analyze frame data
            frame_analyses = analysis_result.get("frame_analysis", [])

            total_faces = 0
            texts_found = []

            for frame_data in frame_analyses:
                analysis = frame_data.get("analysis", {})

                # Count faces
                faces = analysis.get("faces", 0)
                total_faces += faces

                # Collect text
                text = analysis.get("text", "")
                if text and text not in texts_found:
                    texts_found.append(text)

            summary["faces_detected"] = total_faces > 0
            summary["text_found"] = len(texts_found) > 0

            # Generate key findings
            if total_faces > 0:
                summary["key_findings"].append(f"Faces detected in {total_faces} frames")

            if texts_found:
                summary["key_findings"].append(f"Text found: {', '.join(texts_found[:3])}")

            if summary["motion_detected"]:
                motion_intensity = analysis_result.get("motion_analysis", {}).get("motion_intensity", 0)
                summary["key_findings"].append(f"Motion detected (intensity: {motion_intensity:.1f})")

            # Video quality assessment
            video_info = analysis_result.get("video_info", {})
            duration = video_info.get("duration_seconds", 0)
            fps = video_info.get("fps", 0)

            if duration > 0:
                summary["key_findings"].append(f"Duration: {duration:.1f}s, FPS: {fps:.1f}")

        except Exception as e:
            summary["error"] = str(e)

        return summary

    def batch_analyze_images(self, image_paths: List[Union[str, Path]],
                           analysis_types: List[str] = None) -> Dict[str, Any]:
        """Analyze multiple images in batch"""

        results = {
            "batch_info": {
                "total_images": len(image_paths),
                "analysis_types": analysis_types or ["metadata", "ocr", "faces"],
                "timestamp": datetime.now().isoformat()
            },
            "results": [],
            "summary": {}
        }

        successful_analyses = 0
        total_faces = 0
        total_text_extractions = 0

        for i, image_path in enumerate(image_paths):
            try:
                result = self.analyze_image(image_path, analysis_types)
                result["batch_index"] = i
                result["image_path"] = str(image_path)

                results["results"].append(result)

                if "error" not in result:
                    successful_analyses += 1
                    total_faces += result.get("face_analysis", {}).get("face_count", 0)
                    if result.get("text_extraction", {}).get("text", "").strip():
                        total_text_extractions += 1

            except Exception as e:
                results["results"].append({
                    "batch_index": i,
                    "image_path": str(image_path),
                    "error": str(e)
                })

        # Generate batch summary
        results["summary"] = {
            "successful_analyses": successful_analyses,
            "failed_analyses": len(image_paths) - successful_analyses,
            "total_faces_detected": total_faces,
            "images_with_text": total_text_extractions,
            "success_rate": successful_analyses / len(image_paths) if image_paths else 0
        }

        return results
