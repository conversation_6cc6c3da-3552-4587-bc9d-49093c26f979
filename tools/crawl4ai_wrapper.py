"""
Crawl4AI wrapper for structured web crawling and content extraction.

This module provides comprehensive web crawling capabilities using Crawl4AI
for OSINT intelligence gathering. It includes specialized crawlers for different
content types (news, general web pages) with keyword-focused extraction,
structured data processing, and credibility assessment.
"""

import asyncio
import json
import logging
import re
from typing import Dict, List, Any, Optional, Union
from urllib.parse import urlparse, urljoin
from crewai.tools import BaseTool
from pydantic import BaseModel, Field, validator

try:
    from crawl4ai import AsyncWebCrawler
    CRAWL4AI_AVAILABLE = True
except ImportError:
    CRAWL4AI_AVAILABLE = False
    AsyncWebCrawler = None


class Crawl4AIInput(BaseModel):
    """
    Input schema for Crawl4AI tool with comprehensive validation.

    Attributes:
        url: Target URL to crawl and extract content from
        focus_keywords: Keywords to focus extraction on for relevance
        extract_links: Whether to extract and categorize links
        extract_images: Whether to extract image metadata
        max_depth: Maximum crawl depth for recursive crawling
    """
    url: str = Field(
        description="URL to crawl and extract content from",
        min_length=1
    )
    focus_keywords: List[str] = Field(
        default=[],
        description="Keywords to focus extraction on for enhanced relevance"
    )
    extract_links: bool = Field(
        default=True,
        description="Whether to extract and categorize internal/external links"
    )
    extract_images: bool = Field(
        default=False,
        description="Whether to extract image metadata and information"
    )
    max_depth: int = Field(
        default=1,
        description="Maximum crawl depth for recursive crawling",
        ge=1,
        le=3
    )

    @validator('url')
    def validate_url(cls, v: str) -> str:
        """Validate URL format and scheme."""
        if not v.startswith(('http://', 'https://')):
            raise ValueError("URL must start with http:// or https://")

        parsed = urlparse(v)
        if not parsed.netloc:
            raise ValueError("URL must have a valid domain")

        return v

    @validator('focus_keywords')
    def validate_keywords(cls, v: List[str]) -> List[str]:
        """Validate and clean focus keywords."""
        if len(v) > 20:
            raise ValueError("Maximum 20 focus keywords allowed")

        # Clean and filter keywords
        cleaned = [kw.strip().lower() for kw in v if kw.strip()]
        return list(set(cleaned))  # Remove duplicates


class Crawl4AITool(BaseTool):
    """
    CrewAI tool wrapper for Crawl4AI web crawling with advanced features.

    This tool provides comprehensive web crawling capabilities with keyword-focused
    content extraction, structured data processing, and intelligent content analysis
    for OSINT intelligence gathering. It includes robust error handling, content
    validation, and specialized extraction strategies.

    Features:
        - Keyword-focused content extraction
        - Link categorization (internal/external)
        - Image metadata extraction
        - Content credibility assessment
        - Structured data processing
        - Async crawling with timeout handling

    Attributes:
        name: Tool identifier for CrewAI
        description: Tool description for LLM understanding
        args_schema: Pydantic schema for input validation
    """

    name: str = "crawl4ai_web_crawler"
    description: str = """Crawl and extract structured content from web pages with advanced analysis. Useful for gathering detailed information from websites, news articles, blogs, and other web sources. Features keyword-focused extraction, link analysis, and content credibility assessment."""
    args_schema: type[BaseModel] = Crawl4AIInput

    def __init__(self, focus_keywords: Optional[List[str]] = None) -> None:
        """
        Initialize the Crawl4AI tool with optional default keywords.

        Args:
            focus_keywords: Default keywords to focus on during extraction

        Raises:
            ImportError: If Crawl4AI is not available
        """
        super().__init__()
        self.logger = logging.getLogger(__name__)
        self._default_focus_keywords = focus_keywords or []

        # Crawling configuration
        self._timeout = 30
        self._max_retries = 2
        self._user_agent = "OSINT-Agent/1.0 (Intelligence Gathering Bot)"

        if not CRAWL4AI_AVAILABLE:
            raise ImportError(
                "Crawl4AI is not available. Install it with: pip install crawl4ai"
            )

        self.logger.info("Crawl4AI tool initialized successfully")
    
    def _run(
        self,
        url: str,
        focus_keywords: Optional[List[str]] = None,
        extract_links: bool = True,
        extract_images: bool = False,
        max_depth: int = 1
    ) -> str:
        """
        Crawl a web page and extract structured content with comprehensive error handling.

        Args:
            url: URL to crawl and analyze
            focus_keywords: Keywords to focus extraction on for relevance
            extract_links: Whether to extract and categorize links
            extract_images: Whether to extract image metadata
            max_depth: Maximum crawl depth for recursive crawling

        Returns:
            JSON string containing structured crawl results with metadata

        Note:
            Returns error information as JSON if crawling fails
        """
        self.logger.info(f"Starting crawl for URL: {url}")

        try:
            # Validate URL before crawling
            self._validate_url(url)

            # Use default keywords if none provided
            keywords = focus_keywords or self._default_focus_keywords
            self.logger.info(f"Using {len(keywords)} focus keywords")

            # Run async crawling in sync context with proper cleanup
            try:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                result = loop.run_until_complete(
                    self._async_crawl(url, keywords, extract_links, extract_images, max_depth)
                )

                self.logger.info(f"Crawl completed successfully for {url}")
                return json.dumps(result, indent=2, ensure_ascii=False)

            finally:
                loop.close()

        except ValueError as e:
            error_result = self._create_error_result(url, f"Validation error: {str(e)}")
            return json.dumps(error_result, indent=2)
        except asyncio.TimeoutError:
            error_result = self._create_error_result(url, f"Crawl timed out after {self._timeout} seconds")
            return json.dumps(error_result, indent=2)
        except Exception as e:
            self.logger.error(f"Crawl failed for {url}: {str(e)}")
            error_result = self._create_error_result(url, f"Crawl error: {str(e)}")
            return json.dumps(error_result, indent=2)

    def _validate_url(self, url: str) -> None:
        """
        Validate URL before crawling.

        Args:
            url: URL to validate

        Raises:
            ValueError: If URL is invalid or potentially unsafe
        """
        parsed = urlparse(url)

        if not parsed.scheme in ['http', 'https']:
            raise ValueError("Only HTTP and HTTPS URLs are supported")

        if not parsed.netloc:
            raise ValueError("URL must have a valid domain")

        # Basic security checks
        if parsed.netloc.lower() in ['localhost', '127.0.0.1', '0.0.0.0']:
            raise ValueError("Local URLs are not allowed for security reasons")

    def _create_error_result(self, url: str, error_message: str) -> Dict[str, Any]:
        """Create standardized error result structure."""
        return {
            "url": url,
            "success": False,
            "error": error_message,
            "timestamp": asyncio.get_event_loop().time() if asyncio.get_event_loop().is_running() else 0,
            "content": {"text": "", "markdown": "", "word_count": 0},
            "metadata": {"status_code": 0, "crawl_duration": 0},
            "extraction": {}
        }
    
    async def _async_crawl(
        self,
        url: str,
        keywords: List[str],
        extract_links: bool,
        extract_images: bool,
        max_depth: int
    ) -> Dict[str, Any]:
        """
        Async crawling implementation with comprehensive configuration.

        Args:
            url: Target URL to crawl
            keywords: Focus keywords for extraction
            extract_links: Whether to extract links
            extract_images: Whether to extract images
            max_depth: Maximum crawl depth

        Returns:
            Structured crawl results dictionary
        """
        start_time = asyncio.get_event_loop().time()

        # Configure crawler with timeout and user agent
        crawler_config = {
            "verbose": False,
            "headless": True,
            "user_agent": self._user_agent
        }

        async with AsyncWebCrawler(**crawler_config) as crawler:
            # Configure crawling parameters
            crawl_config = {
                "word_count_threshold": 50,
                "extraction_strategy": "LLMExtractionStrategy" if keywords else "NoExtractionStrategy",
                "chunking_strategy": "RegexChunking",
                "bypass_cache": True,
                "process_iframes": False,  # Security consideration
                "remove_overlay_elements": True,
                "simulate_user": True,
                "override_navigator": True
            }

            # Add timeout handling
            try:
                result = await asyncio.wait_for(
                    crawler.arun(url=url, **crawl_config),
                    timeout=self._timeout
                )
            except asyncio.TimeoutError:
                raise asyncio.TimeoutError(f"Crawl timed out after {self._timeout} seconds")

            # Calculate crawl duration
            end_time = asyncio.get_event_loop().time()
            crawl_duration = end_time - start_time

            # Process and structure the results
            structured_result = self._process_crawl_result(
                result, keywords, extract_links, extract_images, crawl_duration
            )

            return structured_result
    
    def _process_crawl_result(
        self,
        result: Any,
        keywords: List[str],
        extract_links: bool,
        extract_images: bool,
        crawl_duration: float = 0.0
    ) -> Dict[str, Any]:
        """
        Process and structure crawl results with comprehensive analysis.

        Args:
            result: Raw crawl result from Crawl4AI
            keywords: Focus keywords for analysis
            extract_links: Whether to process links
            extract_images: Whether to process images
            crawl_duration: Time taken for crawling

        Returns:
            Structured and analyzed crawl results
        """
        # Extract basic content safely
        raw_text = getattr(result, 'cleaned_html', '') or getattr(result, 'text', '')
        markdown_content = getattr(result, 'markdown', '')

        structured = {
            "url": getattr(result, 'url', ''),
            "title": self._clean_text(getattr(result, 'title', '')),
            "success": getattr(result, 'success', False),
            "content": {
                "text": self._clean_text(raw_text),
                "markdown": markdown_content,
                "word_count": len(raw_text.split()) if raw_text else 0,
                "char_count": len(raw_text) if raw_text else 0,
                "language": self._detect_language(raw_text)
            },
            "metadata": {
                "status_code": getattr(result, 'status_code', 0),
                "response_headers": getattr(result, 'response_headers', {}),
                "crawl_timestamp": getattr(result, 'timestamp', ''),
                "crawl_duration": round(crawl_duration, 2),
                "content_type": self._extract_content_type(result),
                "page_size": len(raw_text) if raw_text else 0
            },
            "extraction": {},
            "analysis": {}
        }
        
        # Extract and categorize links if requested
        if extract_links:
            structured["links"] = self._process_links(result, structured["url"])

        # Extract and analyze images if requested
        if extract_images:
            structured["images"] = self._process_images(result)

        # Perform keyword-focused extraction and analysis
        if keywords and raw_text:
            structured["extraction"]["keyword_analysis"] = self._analyze_keywords(
                raw_text, keywords
            )

        # Extract structured data if available
        if hasattr(result, 'extracted_content') and result.extracted_content:
            structured["extraction"]["structured_data"] = result.extracted_content

        # Perform content analysis
        if raw_text:
            structured["analysis"] = self._analyze_content(raw_text)

        # Add quality metrics
        structured["quality_metrics"] = self._calculate_quality_metrics(structured)

        return structured

    def _clean_text(self, text: str) -> str:
        """Clean and normalize text content."""
        if not text:
            return ""

        # Remove excessive whitespace and normalize
        cleaned = re.sub(r'\s+', ' ', text.strip())

        # Remove control characters
        cleaned = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', cleaned)

        return cleaned

    def _detect_language(self, text: str) -> str:
        """Simple language detection based on common words."""
        if not text:
            return "unknown"

        text_lower = text.lower()

        # Simple heuristic-based language detection
        english_indicators = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by']
        english_count = sum(1 for word in english_indicators if word in text_lower)

        if english_count >= 3:
            return "en"

        return "unknown"

    def _extract_content_type(self, result: Any) -> str:
        """Extract content type from response headers."""
        headers = getattr(result, 'response_headers', {})
        if isinstance(headers, dict):
            return headers.get('content-type', 'unknown')
        return "unknown"

    def _process_links(self, result: Any, base_url: str) -> Dict[str, Any]:
        """Process and categorize extracted links."""
        links_data = {
            "internal": [],
            "external": [],
            "total_count": 0,
            "unique_domains": set()
        }

        if hasattr(result, 'links'):
            # Process internal links
            internal_links = getattr(result.links, 'internal', [])
            links_data["internal"] = [self._normalize_link(link, base_url) for link in internal_links]

            # Process external links
            external_links = getattr(result.links, 'external', [])
            for link in external_links:
                normalized = self._normalize_link(link, base_url)
                links_data["external"].append(normalized)

                # Track unique domains
                try:
                    domain = urlparse(normalized).netloc
                    if domain:
                        links_data["unique_domains"].add(domain)
                except:
                    pass

        links_data["total_count"] = len(links_data["internal"]) + len(links_data["external"])
        links_data["unique_domains"] = list(links_data["unique_domains"])

        return links_data

    def _normalize_link(self, link: str, base_url: str) -> str:
        """Normalize and validate a link."""
        if not link:
            return ""

        # Handle relative URLs
        if link.startswith('/'):
            return urljoin(base_url, link)
        elif not link.startswith(('http://', 'https://')):
            return urljoin(base_url, link)

        return link

    def _process_images(self, result: Any) -> Dict[str, Any]:
        """Process and analyze extracted images."""
        images_data = {
            "images": [],
            "total_count": 0,
            "formats": set(),
            "total_size_estimate": 0
        }

        if hasattr(result, 'media') and hasattr(result.media, 'images'):
            images = getattr(result.media, 'images', [])

            for img in images:
                if isinstance(img, dict):
                    processed_img = {
                        "url": img.get("src", ""),
                        "alt_text": img.get("alt", ""),
                        "title": img.get("title", ""),
                        "width": img.get("width", 0),
                        "height": img.get("height", 0)
                    }

                    # Extract format from URL
                    img_url = processed_img["url"]
                    if img_url:
                        try:
                            format_match = re.search(r'\.([a-zA-Z]{3,4})(?:\?|$)', img_url)
                            if format_match:
                                images_data["formats"].add(format_match.group(1).lower())
                        except:
                            pass

                    images_data["images"].append(processed_img)

        images_data["total_count"] = len(images_data["images"])
        images_data["formats"] = list(images_data["formats"])

        return images_data
    
    def _analyze_keywords(self, text: str, keywords: List[str]) -> Dict[str, Any]:
        """Analyze keyword presence and context in the text."""
        text_lower = text.lower()
        analysis = {
            "keyword_matches": {},
            "total_matches": 0,
            "keyword_density": {},
            "relevant_sentences": []
        }
        
        for keyword in keywords:
            keyword_lower = keyword.lower()
            matches = text_lower.count(keyword_lower)
            analysis["keyword_matches"][keyword] = matches
            analysis["total_matches"] += matches
            
            # Calculate keyword density
            word_count = len(text.split())
            if word_count > 0:
                analysis["keyword_density"][keyword] = (matches / word_count) * 100
            
            # Extract sentences containing the keyword
            sentences = text.split('.')
            relevant = [
                sentence.strip() for sentence in sentences 
                if keyword_lower in sentence.lower() and len(sentence.strip()) > 20
            ]
            analysis["relevant_sentences"].extend(relevant[:3])  # Limit to 3 per keyword
        
        return analysis
    



class FocusedNewsCrawler(Crawl4AITool):
    """Specialized crawler for news websites."""

    name: str = "focused_news_crawler"
    description: str = """Crawl news websites and extract article content with focus on specific topics or keywords. Optimized for news article structure."""
    
    def __init__(self):
        super().__init__(focus_keywords=[
            "breaking", "news", "report", "analysis", "investigation",
            "sources", "officials", "statement", "confirmed"
        ])
    
    def _process_crawl_result(self, result, keywords, extract_links, extract_images):
        """Enhanced processing for news articles."""
        structured = super()._process_crawl_result(result, keywords, extract_links, extract_images)
        
        # Add news-specific extraction
        text = structured["content"]["text"]
        structured["extraction"]["news_analysis"] = {
            "article_type": self._classify_article_type(text),
            "key_entities": self._extract_entities(text),
            "publication_indicators": self._find_publication_info(text),
            "credibility_indicators": self._assess_credibility(text)
        }
        
        return structured
    
    def _classify_article_type(self, text: str) -> str:
        """Classify the type of news article."""
        text_lower = text.lower()
        
        if any(word in text_lower for word in ["breaking", "urgent", "alert"]):
            return "breaking_news"
        elif any(word in text_lower for word in ["analysis", "opinion", "editorial"]):
            return "analysis"
        elif any(word in text_lower for word in ["investigation", "exclusive", "revealed"]):
            return "investigative"
        else:
            return "general_news"
    
    def _extract_entities(self, text: str) -> List[str]:
        """Extract key entities from the text."""
        # Simple entity extraction - in production, use NLP libraries
        entities = []
        
        # Look for capitalized words that might be entities
        words = text.split()
        for i, word in enumerate(words):
            if (word[0].isupper() and len(word) > 2 and 
                not word.isupper() and word.isalpha()):
                # Check if it's likely a proper noun
                if i == 0 or not words[i-1].endswith('.'):
                    entities.append(word)
        
        # Remove duplicates and return top entities
        return list(set(entities))[:10]
    
    def _find_publication_info(self, text: str) -> Dict[str, Any]:
        """Find publication date, author, and source information."""
        return {
            "has_byline": "by " in text.lower(),
            "has_dateline": any(month in text.lower() for month in [
                "january", "february", "march", "april", "may", "june",
                "july", "august", "september", "october", "november", "december"
            ]),
            "has_source_attribution": any(phrase in text.lower() for phrase in [
                "according to", "sources say", "reported by", "confirmed by"
            ])
        }
    
    def _assess_credibility(self, text: str) -> Dict[str, Any]:
        """Assess credibility indicators in the article."""
        text_lower = text.lower()
        
        return {
            "has_quotes": '"' in text,
            "has_official_sources": any(phrase in text_lower for phrase in [
                "official", "spokesperson", "government", "ministry", "department"
            ]),
            "has_verification": any(phrase in text_lower for phrase in [
                "verified", "confirmed", "authenticated", "fact-checked"
            ]),
            "speculation_indicators": any(phrase in text_lower for phrase in [
                "allegedly", "reportedly", "rumored", "unconfirmed", "speculation"
            ])
        }
