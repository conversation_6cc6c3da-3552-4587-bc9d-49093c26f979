"""
Serper.dev Google Search API wrapper for OSINT agents.

This module provides comprehensive search capabilities through the Serper.dev API,
which offers access to Google Search results. It includes specialized tools for
different search types (web, news, images) with robust error handling and
result formatting.
"""

import os
import json
import logging
import requests
from typing import Dict, List, Any, Optional, Union
from crewai.tools import BaseTool
from pydantic import BaseModel, Field, validator


class SerperSearchInput(BaseModel):
    """
    Input schema for Serper search tool with validation.

    Attributes:
        query: Search query string (required)
        num_results: Number of results to return (1-100)
        time_range: Time range filter (d1, w1, m1, y1, or empty)
        location: Geographic location code for search
        search_type: Type of search to perform
    """
    query: str = Field(
        description="Search query to execute",
        min_length=1,
        max_length=500
    )
    num_results: int = Field(
        default=10,
        description="Number of results to return",
        ge=1,
        le=100
    )
    time_range: str = Field(
        default="",
        description="Time range filter (d1=past day, w1=past week, m1=past month, y1=past year)"
    )
    location: str = Field(
        default="",
        description="Geographic location code for search (e.g., 'us', 'uk', 'de')"
    )
    search_type: str = Field(
        default="search",
        description="Type of search (search, news, images, videos, places, shopping)"
    )

    @validator('time_range')
    def validate_time_range(cls, v: str) -> str:
        """Validate time range parameter."""
        valid_ranges = ['', 'd1', 'w1', 'm1', 'y1']
        if v and v not in valid_ranges:
            raise ValueError(f"time_range must be one of {valid_ranges}")
        return v

    @validator('search_type')
    def validate_search_type(cls, v: str) -> str:
        """Validate search type parameter."""
        valid_types = ['search', 'news', 'images', 'videos', 'places', 'shopping']
        if v not in valid_types:
            raise ValueError(f"search_type must be one of {valid_types}")
        return v


class SerperSearchTool(BaseTool):
    """
    CrewAI tool wrapper for Serper.dev Google Search API.

    This tool provides comprehensive web search capabilities for OSINT agents
    with support for different search types, time ranges, and geographic filtering.
    It handles API authentication, request formatting, error handling, and
    result processing.

    Features:
        - Multiple search types (web, news, images, videos, places, shopping)
        - Time-based filtering (past day, week, month, year)
        - Geographic location filtering
        - Robust error handling and retry logic
        - Structured result formatting

    Attributes:
        name: Tool identifier for CrewAI
        description: Tool description for LLM understanding
        args_schema: Pydantic schema for input validation
    """

    name: str = "serper_search"
    description: str = """Search the web using Google Search API via Serper.dev. Useful for finding recent news, articles, and information on any topic. Supports time-based filtering and different search types."""
    args_schema: type[BaseModel] = SerperSearchInput

    def __init__(self) -> None:
        """
        Initialize the Serper search tool.

        Raises:
            ValueError: If SERPER_API_KEY environment variable is not set
        """
        super().__init__()
        self.logger = logging.getLogger(__name__)

        api_key = os.getenv("SERPER_API_KEY")
        if not api_key:
            raise ValueError("SERPER_API_KEY environment variable is required")

        self._api_key = api_key
        self._base_url = "https://google.serper.dev"
        self._headers = {
            "X-API-KEY": self._api_key,
            "Content-Type": "application/json"
        }

        # Request configuration
        self._timeout = 30
        self._max_retries = 3

        self.logger.info("Serper search tool initialized successfully")
    
    def _run(
        self,
        query: str,
        num_results: int = 10,
        time_range: str = "",
        location: str = "",
        search_type: str = "search"
    ) -> str:
        """
        Execute a search query using Serper API with comprehensive error handling.

        Args:
            query: Search query string (1-500 characters)
            num_results: Number of results to return (1-100, default: 10)
            time_range: Time range filter (d1, w1, m1, y1, or empty)
            location: Geographic location code (e.g., 'us', 'uk')
            search_type: Type of search to perform

        Returns:
            JSON string containing formatted search results with metadata

        Note:
            Returns error message as string if request fails
        """
        self.logger.info(f"Executing {search_type} search for: '{query[:50]}...'")

        try:
            # Validate and prepare search parameters
            params = self._prepare_search_params(query, num_results, time_range, location)

            # Get API endpoint for search type
            url = self._get_search_endpoint(search_type)

            # Execute search with retry logic
            data = self._execute_search_request(url, params)

            # Format and return results
            formatted_results = self._format_results(data, search_type)

            self.logger.info(f"Search completed successfully, found {len(formatted_results.get('results', []))} results")
            return json.dumps(formatted_results, indent=2)

        except requests.exceptions.Timeout:
            error_msg = f"Search request timed out after {self._timeout} seconds"
            self.logger.error(error_msg)
            return f"Error: {error_msg}"
        except requests.exceptions.HTTPError as e:
            error_msg = f"HTTP error {e.response.status_code}: {e.response.text}"
            self.logger.error(error_msg)
            return f"Error: {error_msg}"
        except requests.exceptions.RequestException as e:
            error_msg = f"Network error during search request: {str(e)}"
            self.logger.error(error_msg)
            return f"Error: {error_msg}"
        except json.JSONDecodeError as e:
            error_msg = f"Failed to parse API response: {str(e)}"
            self.logger.error(error_msg)
            return f"Error: {error_msg}"
        except Exception as e:
            error_msg = f"Unexpected error during search: {str(e)}"
            self.logger.error(error_msg)
            return f"Error: {error_msg}"

    def _prepare_search_params(
        self,
        query: str,
        num_results: int,
        time_range: str,
        location: str
    ) -> Dict[str, Any]:
        """Prepare and validate search parameters."""
        params = {
            "q": query.strip(),
            "num": min(max(num_results, 1), 100)  # Clamp to valid range
        }

        # Add optional parameters
        if time_range:
            params["tbs"] = f"qdr:{time_range}"
        if location:
            params["gl"] = location.lower()

        return params

    def _get_search_endpoint(self, search_type: str) -> str:
        """Get the appropriate API endpoint for the search type."""
        endpoint_map = {
            "search": "/search",
            "news": "/news",
            "images": "/images",
            "videos": "/videos",
            "places": "/places",
            "shopping": "/shopping"
        }

        endpoint = endpoint_map.get(search_type, "/search")
        return f"{self._base_url}{endpoint}"

    def _execute_search_request(self, url: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the search request with retry logic."""
        for attempt in range(self._max_retries):
            try:
                response = requests.post(
                    url,
                    headers=self._headers,
                    json=params,
                    timeout=self._timeout
                )
                response.raise_for_status()
                return response.json()

            except (requests.exceptions.Timeout, requests.exceptions.ConnectionError) as e:
                if attempt < self._max_retries - 1:
                    self.logger.warning(f"Request attempt {attempt + 1} failed, retrying: {str(e)}")
                    continue
                raise

        raise requests.exceptions.RequestException("Max retries exceeded")
    
    def _format_results(self, data: Dict[str, Any], search_type: str) -> Dict[str, Any]:
        """
        Format search results for better readability and analysis.

        Args:
            data: Raw API response data
            search_type: Type of search performed

        Returns:
            Formatted results dictionary with structured data
        """
        # Initialize formatted results structure
        formatted = {
            "search_type": search_type,
            "query": data.get("searchParameters", {}).get("q", ""),
            "total_results": self._safe_get_int(data, ["searchInformation", "totalResults"]),
            "search_time": self._safe_get_float(data, ["searchInformation", "searchTime"]),
            "timestamp": data.get("searchInformation", {}).get("formattedSearchTime", ""),
            "results": [],
            "metadata": {}
        }

        # Format organic/main results
        if "organic" in data:
            formatted["results"] = self._format_organic_results(data["organic"])

        # Format news results (for news searches or mixed results)
        if "news" in data:
            formatted["news"] = self._format_news_results(data["news"])

        # Format image results
        if "images" in data:
            formatted["images"] = self._format_image_results(data["images"])

        # Add knowledge graph information
        if "knowledgeGraph" in data:
            formatted["knowledge_graph"] = self._format_knowledge_graph(data["knowledgeGraph"])

        # Add related searches for query expansion
        if "relatedSearches" in data:
            formatted["related_searches"] = [
                search.get("query", "") for search in data["relatedSearches"]
            ]

        # Add answer box if available
        if "answerBox" in data:
            formatted["answer_box"] = self._format_answer_box(data["answerBox"])

        # Add people also ask section
        if "peopleAlsoAsk" in data:
            formatted["people_also_ask"] = [
                {"question": item.get("question", ""), "snippet": item.get("snippet", "")}
                for item in data["peopleAlsoAsk"]
            ]

        # Store additional metadata
        formatted["metadata"] = {
            "result_count": len(formatted["results"]),
            "has_knowledge_graph": "knowledgeGraph" in data,
            "has_answer_box": "answerBox" in data,
            "search_parameters": data.get("searchParameters", {})
        }

        return formatted

    def _format_organic_results(self, organic_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Format organic search results."""
        formatted_results = []
        for result in organic_results:
            formatted_result = {
                "title": result.get("title", "").strip(),
                "link": result.get("link", ""),
                "snippet": result.get("snippet", "").strip(),
                "date": result.get("date", ""),
                "position": result.get("position", 0),
                "displayed_link": result.get("displayedLink", ""),
                "favicon": result.get("favicon", "")
            }

            # Add sitelinks if available
            if "sitelinks" in result:
                formatted_result["sitelinks"] = [
                    {"title": link.get("title", ""), "link": link.get("link", "")}
                    for link in result["sitelinks"]
                ]

            formatted_results.append(formatted_result)

        return formatted_results

    def _format_news_results(self, news_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Format news search results."""
        return [
            {
                "title": news_item.get("title", "").strip(),
                "link": news_item.get("link", ""),
                "snippet": news_item.get("snippet", "").strip(),
                "date": news_item.get("date", ""),
                "source": news_item.get("source", ""),
                "image_url": news_item.get("imageUrl", ""),
                "position": news_item.get("position", 0)
            }
            for news_item in news_results
        ]

    def _format_image_results(self, image_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Format image search results."""
        return [
            {
                "title": img.get("title", "").strip(),
                "image_url": img.get("imageUrl", ""),
                "image_width": img.get("imageWidth", 0),
                "image_height": img.get("imageHeight", 0),
                "thumbnail_url": img.get("thumbnailUrl", ""),
                "source": img.get("source", ""),
                "link": img.get("link", ""),
                "position": img.get("position", 0)
            }
            for img in image_results
        ]

    def _format_knowledge_graph(self, kg_data: Dict[str, Any]) -> Dict[str, Any]:
        """Format knowledge graph information."""
        return {
            "title": kg_data.get("title", "").strip(),
            "type": kg_data.get("type", ""),
            "description": kg_data.get("description", "").strip(),
            "description_source": kg_data.get("descriptionSource", ""),
            "description_link": kg_data.get("descriptionLink", ""),
            "image_url": kg_data.get("imageUrl", ""),
            "attributes": kg_data.get("attributes", {}),
            "profiles": kg_data.get("profiles", [])
        }

    def _format_answer_box(self, answer_data: Dict[str, Any]) -> Dict[str, Any]:
        """Format answer box information."""
        return {
            "answer": answer_data.get("answer", "").strip(),
            "title": answer_data.get("title", "").strip(),
            "link": answer_data.get("link", ""),
            "snippet": answer_data.get("snippet", "").strip()
        }

    def _safe_get_int(self, data: Dict[str, Any], keys: List[str], default: int = 0) -> int:
        """Safely extract integer value from nested dictionary."""
        try:
            value = data
            for key in keys:
                value = value[key]
            return int(value) if value is not None else default
        except (KeyError, TypeError, ValueError):
            return default

    def _safe_get_float(self, data: Dict[str, Any], keys: List[str], default: float = 0.0) -> float:
        """Safely extract float value from nested dictionary."""
        try:
            value = data
            for key in keys:
                value = value[key]
            return float(value) if value is not None else default
        except (KeyError, TypeError, ValueError):
            return default
    



class SerperNewsSearchTool(SerperSearchTool):
    """
    Specialized tool for news search with optimized defaults.

    This tool is specifically configured for news searches with
    appropriate default time ranges and enhanced news result formatting.
    """

    name: str = "serper_news_search"
    description: str = """Search for news articles using Google News via Serper.dev. Optimized for finding recent news and current events with enhanced news-specific formatting. Default time range is past day."""

    def _run(self, query: str, num_results: int = 10, time_range: str = "d1", **kwargs) -> str:
        """
        Search for news with optimized defaults for news content.

        Args:
            query: News search query
            num_results: Number of news articles to return (default: 10)
            time_range: Time range for news (default: "d1" for past day)
            **kwargs: Additional search parameters

        Returns:
            JSON string with formatted news results
        """
        self.logger.info(f"Executing news search for: '{query[:50]}...'")
        return super()._run(
            query=query,
            num_results=num_results,
            time_range=time_range,
            search_type="news",
            **kwargs
        )


class SerperImageSearchTool(SerperSearchTool):
    """
    Specialized tool for image search and visual intelligence gathering.

    This tool is optimized for image searches with enhanced image
    metadata extraction and visual content analysis capabilities.
    """

    name: str = "serper_image_search"
    description: str = """Search for images using Google Images via Serper.dev. Optimized for visual intelligence gathering, verification, and image analysis with enhanced metadata extraction."""

    def _run(self, query: str, num_results: int = 10, **kwargs) -> str:
        """
        Search for images with enhanced metadata extraction.

        Args:
            query: Image search query
            num_results: Number of images to return (default: 10)
            **kwargs: Additional search parameters

        Returns:
            JSON string with formatted image results including metadata
        """
        self.logger.info(f"Executing image search for: '{query[:50]}...'")
        return super()._run(
            query=query,
            num_results=num_results,
            search_type="images",
            **kwargs
        )


class SerperVideoSearchTool(SerperSearchTool):
    """
    Specialized tool for video search and multimedia intelligence.

    This tool is optimized for video searches with enhanced video
    metadata extraction and multimedia content analysis.
    """

    name: str = "serper_video_search"
    description: str = """Search for videos using Google Video Search via Serper.dev. Useful for multimedia intelligence gathering and video content analysis."""

    def _run(self, query: str, num_results: int = 10, **kwargs) -> str:
        """
        Search for videos with enhanced metadata extraction.

        Args:
            query: Video search query
            num_results: Number of videos to return (default: 10)
            **kwargs: Additional search parameters

        Returns:
            JSON string with formatted video results including metadata
        """
        self.logger.info(f"Executing video search for: '{query[:50]}...'")
        return super()._run(
            query=query,
            num_results=num_results,
            search_type="videos",
            **kwargs
        )
