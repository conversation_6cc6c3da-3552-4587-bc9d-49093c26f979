"""
🧠 CrewAI OSINT Agent Framework - Streamlit UI

Interactive web interface for the OSINT framework with agent selection,
input forms, and result visualization.
"""

import streamlit as st
import json
import os
import sys
from datetime import datetime
from pathlib import Path
import plotly.express as px
import plotly.graph_objects as go
import pandas as pd

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from agents.geo_agent import GeopoliticalAgent
from agents.cti_agent import CTIAgent
from workflows.langchain_geo_workflow import GeopoliticalWorkflow
from workflows.llamaindex_cti_workflow import CTIWorkflow
from rag.index_builder import IndexBuilder
from rag.retriever import DocumentRetriever

# Page configuration
st.set_page_config(
    page_title="🧠 OSINT Agent Framework",
    page_icon="🧠",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .agent-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }
    .metric-card {
        background-color: #ffffff;
        padding: 1rem;
        border-radius: 0.5rem;
        border: 1px solid #e0e0e0;
        margin: 0.5rem 0;
    }
    .success-box {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }
    .warning-box {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        color: #856404;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

def initialize_session_state():
    """Initialize session state variables"""
    if 'analysis_history' not in st.session_state:
        st.session_state.analysis_history = []
    if 'current_agent' not in st.session_state:
        st.session_state.current_agent = None
    if 'api_keys_configured' not in st.session_state:
        st.session_state.api_keys_configured = check_api_keys()

def check_api_keys():
    """Check if required API keys are configured"""
    openai_key = os.getenv('OPENAI_API_KEY')
    serper_key = os.getenv('SERPER_API_KEY')
    return bool(openai_key and serper_key)

def display_header():
    """Display the main header"""
    st.markdown('<h1 class="main-header">🧠 OSINT Agent Framework</h1>', unsafe_allow_html=True)
    st.markdown("**Advanced Open Source Intelligence Analysis with AI Agents**")
    
    # API Key status
    if st.session_state.api_keys_configured:
        st.markdown('<div class="success-box">✅ API Keys Configured</div>', unsafe_allow_html=True)
    else:
        st.markdown('<div class="warning-box">⚠️ API Keys Not Configured - Check your .env file</div>', unsafe_allow_html=True)

def sidebar_navigation():
    """Create sidebar navigation"""
    st.sidebar.title("🔧 Navigation")
    
    page = st.sidebar.selectbox(
        "Select Page",
        ["🏠 Home", "🌍 Geopolitical Analysis", "🔒 Cyber Threat Intelligence", 
         "📚 Document Management", "📊 Analytics Dashboard", "⚙️ Settings"]
    )
    
    st.sidebar.markdown("---")
    st.sidebar.markdown("### 📈 Quick Stats")
    
    # Display quick stats
    if st.session_state.analysis_history:
        total_analyses = len(st.session_state.analysis_history)
        geo_analyses = len([a for a in st.session_state.analysis_history if a.get('type') == 'geopolitical'])
        cti_analyses = len([a for a in st.session_state.analysis_history if a.get('type') == 'cti'])
        
        st.sidebar.metric("Total Analyses", total_analyses)
        st.sidebar.metric("Geopolitical", geo_analyses)
        st.sidebar.metric("CTI", cti_analyses)
    else:
        st.sidebar.info("No analyses yet")
    
    return page

def home_page():
    """Display home page"""
    st.markdown("## 🎯 Welcome to the OSINT Framework")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        ### 🌍 Geopolitical Intelligence
        - International relations analysis
        - Regional monitoring
        - Political development tracking
        - Intelligence brief generation
        """)
        
        if st.button("🚀 Start Geopolitical Analysis", key="geo_start"):
            st.session_state.current_page = "🌍 Geopolitical Analysis"
            st.rerun()
    
    with col2:
        st.markdown("""
        ### 🔒 Cyber Threat Intelligence
        - IOC extraction and analysis
        - Threat actor tracking
        - Campaign correlation
        - Security report generation
        """)
        
        if st.button("🚀 Start CTI Analysis", key="cti_start"):
            st.session_state.current_page = "🔒 Cyber Threat Intelligence"
            st.rerun()
    
    # Recent analyses
    if st.session_state.analysis_history:
        st.markdown("## 📋 Recent Analyses")
        
        for i, analysis in enumerate(st.session_state.analysis_history[-5:]):
            with st.expander(f"{analysis.get('type', 'Unknown').title()} - {analysis.get('timestamp', 'Unknown')}"):
                st.json(analysis)

def geopolitical_page():
    """Display geopolitical analysis page"""
    st.markdown("## 🌍 Geopolitical Intelligence Analysis")
    
    if not st.session_state.api_keys_configured:
        st.error("Please configure your API keys in the .env file to use this feature.")
        return
    
    # Analysis type selection
    analysis_type = st.selectbox(
        "Select Analysis Type",
        ["Intelligence Brief", "Regional Monitoring", "Situation Report", "Custom Analysis"]
    )
    
    if analysis_type == "Intelligence Brief":
        geopolitical_intelligence_brief()
    elif analysis_type == "Regional Monitoring":
        geopolitical_regional_monitoring()
    elif analysis_type == "Situation Report":
        geopolitical_situation_report()
    else:
        geopolitical_custom_analysis()

def geopolitical_intelligence_brief():
    """Generate intelligence brief"""
    st.markdown("### 📋 Intelligence Brief Generation")
    
    col1, col2 = st.columns(2)
    
    with col1:
        topic = st.text_input("Topic", placeholder="e.g., Middle East tensions")
        regions = st.multiselect(
            "Regions of Interest",
            ["Middle East", "Eastern Europe", "Asia-Pacific", "Africa", "Americas", "Western Europe"]
        )
    
    with col2:
        time_range = st.selectbox("Time Range", ["24h", "7d", "30d", "90d"])
        priority_level = st.selectbox("Priority Level", ["High", "Medium", "Low"])
    
    if st.button("🔍 Generate Intelligence Brief"):
        if topic and regions:
            with st.spinner("Generating intelligence brief..."):
                try:
                    # Initialize agent and workflow
                    geo_agent = GeopoliticalAgent()
                    workflow = GeopoliticalWorkflow()
                    
                    # Generate brief
                    brief = geo_agent.generate_intelligence_brief(
                        topic=topic,
                        regions=regions,
                        time_range=time_range
                    )
                    
                    # Display results
                    st.success("Intelligence brief generated successfully!")
                    st.markdown("### 📄 Intelligence Brief")
                    st.markdown(brief)
                    
                    # Save to history
                    analysis_record = {
                        "type": "geopolitical",
                        "subtype": "intelligence_brief",
                        "topic": topic,
                        "regions": regions,
                        "time_range": time_range,
                        "result": brief,
                        "timestamp": datetime.now().isoformat()
                    }
                    st.session_state.analysis_history.append(analysis_record)
                    
                except Exception as e:
                    st.error(f"Error generating intelligence brief: {str(e)}")
        else:
            st.warning("Please provide both topic and regions.")

def geopolitical_regional_monitoring():
    """Set up regional monitoring"""
    st.markdown("### 🌐 Regional Monitoring Setup")
    
    regions = st.multiselect(
        "Select Regions to Monitor",
        ["Middle East", "Eastern Europe", "Asia-Pacific", "Africa", "Americas", "Western Europe"],
        default=["Middle East", "Eastern Europe"]
    )
    
    keywords = st.text_area(
        "Keywords to Monitor",
        placeholder="Enter keywords separated by commas (e.g., conflict, diplomacy, sanctions)"
    )
    
    update_frequency = st.selectbox(
        "Update Frequency",
        ["Real-time", "Hourly", "Daily", "Weekly"]
    )
    
    if st.button("🚀 Start Regional Monitoring"):
        if regions:
            with st.spinner("Setting up regional monitoring..."):
                try:
                    geo_agent = GeopoliticalAgent()
                    monitoring_setup = geo_agent.setup_regional_monitoring(
                        regions=regions,
                        keywords=keywords.split(',') if keywords else [],
                        frequency=update_frequency
                    )
                    
                    st.success("Regional monitoring setup completed!")
                    st.json(monitoring_setup)
                    
                except Exception as e:
                    st.error(f"Error setting up monitoring: {str(e)}")
        else:
            st.warning("Please select at least one region to monitor.")

def geopolitical_situation_report():
    """Generate situation report"""
    st.markdown("### 📊 Multi-Region Situation Report")
    
    regions = st.multiselect(
        "Select Regions",
        ["Asia", "Europe", "Americas", "Africa", "Middle East", "Oceania"],
        default=["Asia", "Europe", "Americas"]
    )
    
    time_range = st.selectbox("Time Range", ["24h", "7d", "30d"])
    
    if st.button("📋 Generate Situation Report"):
        if regions:
            with st.spinner("Generating situation report..."):
                try:
                    workflow = GeopoliticalWorkflow()
                    report = workflow.generate_situation_report(
                        regions=regions,
                        time_range=time_range
                    )
                    
                    st.success("Situation report generated!")
                    
                    # Display report sections
                    if isinstance(report, dict):
                        for region in regions:
                            if region.lower() in report.get('regional_analyses', {}):
                                st.markdown(f"### 🌍 {region}")
                                st.markdown(report['regional_analyses'][region.lower()])
                        
                        if 'summary_assessment' in report:
                            st.markdown("### 📋 Summary Assessment")
                            st.markdown(report['summary_assessment'])
                    else:
                        st.markdown(report)
                    
                except Exception as e:
                    st.error(f"Error generating situation report: {str(e)}")
        else:
            st.warning("Please select at least one region.")

def geopolitical_custom_analysis():
    """Custom geopolitical analysis"""
    st.markdown("### 🔧 Custom Geopolitical Analysis")
    
    query = st.text_area(
        "Analysis Query",
        placeholder="Enter your specific geopolitical analysis request..."
    )
    
    context = st.text_area(
        "Additional Context (Optional)",
        placeholder="Provide any additional context or constraints..."
    )
    
    if st.button("🔍 Run Custom Analysis"):
        if query:
            with st.spinner("Running custom analysis..."):
                try:
                    geo_agent = GeopoliticalAgent()
                    result = geo_agent.analyze_geopolitical_situation(
                        query=query,
                        context=context
                    )
                    
                    st.success("Custom analysis completed!")
                    st.markdown("### 📄 Analysis Results")
                    st.markdown(result)
                    
                except Exception as e:
                    st.error(f"Error running custom analysis: {str(e)}")
        else:
            st.warning("Please provide an analysis query.")

def cti_page():
    """Display cyber threat intelligence page"""
    st.markdown("## 🔒 Cyber Threat Intelligence Analysis")

    if not st.session_state.api_keys_configured:
        st.error("Please configure your API keys in the .env file to use this feature.")
        return

    # Analysis type selection
    analysis_type = st.selectbox(
        "Select Analysis Type",
        ["IOC Extraction", "Threat Actor Tracking", "Campaign Analysis", "IOC Report Generation"]
    )

    if analysis_type == "IOC Extraction":
        cti_ioc_extraction()
    elif analysis_type == "Threat Actor Tracking":
        cti_threat_actor_tracking()
    elif analysis_type == "Campaign Analysis":
        cti_campaign_analysis()
    else:
        cti_ioc_report_generation()

def cti_ioc_extraction():
    """IOC extraction interface"""
    st.markdown("### 🔍 IOC Extraction")

    input_method = st.radio(
        "Input Method",
        ["Text Input", "File Upload", "URL Analysis"]
    )

    threat_data = ""

    if input_method == "Text Input":
        threat_data = st.text_area(
            "Threat Report Text",
            placeholder="Paste threat report, security alert, or any text containing potential IOCs...",
            height=200
        )
    elif input_method == "File Upload":
        uploaded_file = st.file_uploader(
            "Upload Threat Report",
            type=['txt', 'pdf', 'docx', 'md']
        )
        if uploaded_file:
            threat_data = str(uploaded_file.read(), "utf-8")
            st.text_area("File Content Preview", threat_data[:500] + "...", height=100)
    else:
        url = st.text_input("URL to Analyze", placeholder="https://example.com/threat-report")
        if url and st.button("Fetch Content"):
            st.info("URL fetching functionality would be implemented here")

    if st.button("🔍 Extract IOCs"):
        if threat_data:
            with st.spinner("Extracting IOCs..."):
                try:
                    cti_agent = CTIAgent()
                    iocs = cti_agent.extract_iocs(threat_data)

                    st.success(f"Extracted {len(iocs)} IOCs!")

                    # Display IOCs by type
                    ioc_types = {}
                    for ioc in iocs:
                        ioc_type = ioc.get('type', 'unknown')
                        if ioc_type not in ioc_types:
                            ioc_types[ioc_type] = []
                        ioc_types[ioc_type].append(ioc)

                    for ioc_type, ioc_list in ioc_types.items():
                        st.markdown(f"#### {ioc_type.upper()} ({len(ioc_list)})")
                        for ioc in ioc_list:
                            st.code(ioc.get('value', 'N/A'))

                    # Save to history
                    analysis_record = {
                        "type": "cti",
                        "subtype": "ioc_extraction",
                        "input_length": len(threat_data),
                        "iocs_found": len(iocs),
                        "ioc_types": list(ioc_types.keys()),
                        "timestamp": datetime.now().isoformat()
                    }
                    st.session_state.analysis_history.append(analysis_record)

                except Exception as e:
                    st.error(f"Error extracting IOCs: {str(e)}")
        else:
            st.warning("Please provide threat data to analyze.")

def cti_threat_actor_tracking():
    """Threat actor tracking interface"""
    st.markdown("### 👤 Threat Actor Tracking")

    actor_name = st.text_input(
        "Threat Actor Name",
        placeholder="e.g., APT28, Lazarus, Carbanak"
    )

    search_type = st.selectbox(
        "Search Type",
        ["Known Profile", "Recent Activity", "TTPs Analysis", "Attribution Research"]
    )

    if st.button("🔍 Track Threat Actor"):
        if actor_name:
            with st.spinner(f"Tracking {actor_name}..."):
                try:
                    cti_agent = CTIAgent()
                    profile = cti_agent.track_threat_actor(actor_name)

                    st.success(f"Threat actor profile for {actor_name} retrieved!")

                    # Display profile information
                    if isinstance(profile, dict):
                        col1, col2 = st.columns(2)

                        with col1:
                            st.markdown("#### 📋 Basic Information")
                            st.write(f"**Name:** {profile.get('name', 'N/A')}")
                            st.write(f"**Aliases:** {', '.join(profile.get('aliases', []))}")
                            st.write(f"**Origin:** {profile.get('origin', 'Unknown')}")
                            st.write(f"**Active Since:** {profile.get('active_since', 'Unknown')}")

                        with col2:
                            st.markdown("#### 🎯 Targets & TTPs")
                            if 'targets' in profile:
                                st.write("**Primary Targets:**")
                                for target in profile['targets']:
                                    st.write(f"- {target}")

                            if 'ttps' in profile:
                                st.write("**Known TTPs:**")
                                for ttp in profile['ttps']:
                                    st.write(f"- {ttp}")
                    else:
                        st.markdown(profile)

                except Exception as e:
                    st.error(f"Error tracking threat actor: {str(e)}")
        else:
            st.warning("Please provide a threat actor name.")

def cti_campaign_analysis():
    """Campaign analysis interface"""
    st.markdown("### 📊 Campaign Analysis")

    campaign_data = st.text_area(
        "Campaign Information",
        placeholder="Describe the campaign or paste related threat intelligence...",
        height=150
    )

    analysis_focus = st.multiselect(
        "Analysis Focus",
        ["IOC Correlation", "Timeline Analysis", "Attribution", "Impact Assessment", "Mitigation Recommendations"]
    )

    if st.button("📊 Analyze Campaign"):
        if campaign_data:
            with st.spinner("Analyzing campaign..."):
                try:
                    cti_agent = CTIAgent()
                    analysis = cti_agent.correlate_campaign_data(campaign_data, analysis_focus)

                    st.success("Campaign analysis completed!")
                    st.markdown("### 📄 Analysis Results")
                    st.markdown(analysis)

                except Exception as e:
                    st.error(f"Error analyzing campaign: {str(e)}")
        else:
            st.warning("Please provide campaign information.")

def cti_ioc_report_generation():
    """IOC report generation interface"""
    st.markdown("### 📋 IOC Report Generation")

    col1, col2 = st.columns(2)

    with col1:
        ioc_input = st.text_area(
            "IOCs (one per line)",
            placeholder="192.168.1.1\nexample.com\nhash123...",
            height=200
        )

    with col2:
        context = st.text_area(
            "Context/Campaign Info",
            placeholder="Provide context about these IOCs...",
            height=200
        )

    report_format = st.selectbox(
        "Report Format",
        ["Detailed Analysis", "Executive Summary", "Technical Brief", "STIX/TAXII Format"]
    )

    if st.button("📋 Generate IOC Report"):
        if ioc_input:
            with st.spinner("Generating IOC report..."):
                try:
                    cti_agent = CTIAgent()

                    # Parse IOCs
                    ioc_list = [ioc.strip() for ioc in ioc_input.split('\n') if ioc.strip()]

                    # Generate report
                    report = cti_agent.generate_ioc_report(ioc_list, context)

                    st.success("IOC report generated!")
                    st.markdown("### 📄 IOC Report")
                    st.markdown(report)

                    # Offer download
                    st.download_button(
                        label="📥 Download Report",
                        data=report,
                        file_name=f"ioc_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md",
                        mime="text/markdown"
                    )

                except Exception as e:
                    st.error(f"Error generating IOC report: {str(e)}")
        else:
            st.warning("Please provide IOCs to analyze.")

def document_management_page():
    """Document management interface"""
    st.markdown("## 📚 Document Management")

    tab1, tab2, tab3 = st.tabs(["📤 Upload Documents", "🔍 Search Documents", "📊 Index Status"])

    with tab1:
        st.markdown("### 📤 Document Upload & Indexing")

        upload_method = st.radio(
            "Upload Method",
            ["File Upload", "URL Ingestion", "Text Input"]
        )

        if upload_method == "File Upload":
            uploaded_files = st.file_uploader(
                "Upload Documents",
                type=['txt', 'pdf', 'docx', 'md', 'json'],
                accept_multiple_files=True
            )

            if uploaded_files and st.button("📚 Index Documents"):
                with st.spinner("Indexing documents..."):
                    try:
                        index_builder = IndexBuilder()

                        # Process uploaded files
                        documents = []
                        for file in uploaded_files:
                            content = str(file.read(), "utf-8")
                            documents.append({
                                "content": content,
                                "metadata": {"filename": file.name, "type": "upload"}
                            })

                        # Build index
                        index = index_builder.build_index(documents=documents)

                        st.success(f"Successfully indexed {len(documents)} documents!")

                    except Exception as e:
                        st.error(f"Error indexing documents: {str(e)}")

        elif upload_method == "URL Ingestion":
            urls = st.text_area(
                "URLs (one per line)",
                placeholder="https://example.com/report1\nhttps://example.com/report2"
            )

            if urls and st.button("🌐 Crawl & Index URLs"):
                with st.spinner("Crawling and indexing URLs..."):
                    try:
                        index_builder = IndexBuilder()
                        url_list = [url.strip() for url in urls.split('\n') if url.strip()]

                        index = index_builder.build_index(urls=url_list)

                        st.success(f"Successfully crawled and indexed {len(url_list)} URLs!")

                    except Exception as e:
                        st.error(f"Error crawling URLs: {str(e)}")

        else:  # Text Input
            text_content = st.text_area(
                "Text Content",
                placeholder="Paste text content to index...",
                height=200
            )

            doc_title = st.text_input("Document Title", placeholder="Optional title")

            if text_content and st.button("📝 Index Text"):
                with st.spinner("Indexing text..."):
                    try:
                        index_builder = IndexBuilder()

                        documents = [{
                            "content": text_content,
                            "metadata": {"title": doc_title or "Manual Input", "type": "text"}
                        }]

                        index = index_builder.build_index(documents=documents)

                        st.success("Text successfully indexed!")

                    except Exception as e:
                        st.error(f"Error indexing text: {str(e)}")

    with tab2:
        st.markdown("### 🔍 Document Search")

        search_query = st.text_input(
            "Search Query",
            placeholder="Enter search terms..."
        )

        col1, col2 = st.columns(2)
        with col1:
            search_type = st.selectbox("Search Type", ["Semantic", "Keyword", "Hybrid"])
            max_results = st.slider("Max Results", 1, 20, 5)

        with col2:
            date_filter = st.date_input("Date Filter (Optional)")
            doc_type_filter = st.multiselect("Document Type", ["upload", "url", "text"])

        if search_query and st.button("🔍 Search"):
            with st.spinner("Searching documents..."):
                try:
                    retriever = DocumentRetriever()

                    results = retriever.search(
                        query=search_query,
                        search_type=search_type.lower(),
                        max_results=max_results
                    )

                    st.success(f"Found {len(results)} results!")

                    for i, result in enumerate(results):
                        with st.expander(f"Result {i+1}: {result.get('title', 'Untitled')}"):
                            st.markdown(f"**Score:** {result.get('score', 'N/A')}")
                            st.markdown(f"**Content:** {result.get('content', '')[:500]}...")
                            if result.get('metadata'):
                                st.json(result['metadata'])

                except Exception as e:
                    st.error(f"Error searching documents: {str(e)}")

    with tab3:
        st.markdown("### 📊 Index Status")

        if st.button("🔄 Refresh Status"):
            try:
                # This would show index statistics
                st.info("Index status functionality would be implemented here")

                # Mock data for demonstration
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("Total Documents", "156")
                with col2:
                    st.metric("Index Size", "2.3 GB")
                with col3:
                    st.metric("Last Updated", "2 hours ago")

            except Exception as e:
                st.error(f"Error getting index status: {str(e)}")

def analytics_dashboard_page():
    """Analytics dashboard"""
    st.markdown("## 📊 Analytics Dashboard")

    if not st.session_state.analysis_history:
        st.info("No analysis data available yet. Run some analyses to see dashboard.")
        return

    # Convert analysis history to DataFrame
    df = pd.DataFrame(st.session_state.analysis_history)

    # Overview metrics
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("Total Analyses", len(df))

    with col2:
        geo_count = len(df[df['type'] == 'geopolitical'])
        st.metric("Geopolitical", geo_count)

    with col3:
        cti_count = len(df[df['type'] == 'cti'])
        st.metric("CTI Analyses", cti_count)

    with col4:
        if 'timestamp' in df.columns:
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            today_count = len(df[df['timestamp'].dt.date == datetime.now().date()])
            st.metric("Today", today_count)

    # Analysis type distribution
    st.markdown("### 📊 Analysis Distribution")

    if 'type' in df.columns:
        type_counts = df['type'].value_counts()
        fig_pie = px.pie(
            values=type_counts.values,
            names=type_counts.index,
            title="Analysis Types"
        )
        st.plotly_chart(fig_pie, use_container_width=True)

    # Timeline analysis
    if 'timestamp' in df.columns:
        st.markdown("### 📈 Analysis Timeline")

        df['date'] = df['timestamp'].dt.date
        daily_counts = df.groupby(['date', 'type']).size().reset_index(name='count')

        fig_timeline = px.line(
            daily_counts,
            x='date',
            y='count',
            color='type',
            title="Daily Analysis Count"
        )
        st.plotly_chart(fig_timeline, use_container_width=True)

    # Recent activity
    st.markdown("### 🕒 Recent Activity")

    recent_analyses = df.tail(10).sort_values('timestamp', ascending=False)

    for _, analysis in recent_analyses.iterrows():
        with st.expander(f"{analysis['type'].title()} - {analysis.get('subtype', 'N/A')} ({analysis['timestamp']})"):
            st.json(analysis.to_dict())

def settings_page():
    """Settings and configuration page"""
    st.markdown("## ⚙️ Settings & Configuration")

    tab1, tab2, tab3 = st.tabs(["🔑 API Keys", "🔧 Framework Settings", "📊 Data Management"])

    with tab1:
        st.markdown("### 🔑 API Key Configuration")

        # API Key status
        openai_key = os.getenv('OPENAI_API_KEY')
        serper_key = os.getenv('SERPER_API_KEY')

        col1, col2 = st.columns(2)

        with col1:
            st.markdown("#### OpenAI API Key")
            if openai_key:
                st.success("✅ Configured")
                masked_key = openai_key[:8] + "..." + openai_key[-4:] if len(openai_key) > 12 else "***"
                st.code(masked_key)
            else:
                st.error("❌ Not configured")

            new_openai_key = st.text_input(
                "Update OpenAI API Key",
                type="password",
                placeholder="sk-..."
            )

        with col2:
            st.markdown("#### Serper API Key")
            if serper_key:
                st.success("✅ Configured")
                masked_key = serper_key[:8] + "..." + serper_key[-4:] if len(serper_key) > 12 else "***"
                st.code(masked_key)
            else:
                st.error("❌ Not configured")

            new_serper_key = st.text_input(
                "Update Serper API Key",
                type="password",
                placeholder="Your Serper API key"
            )

        if st.button("💾 Update API Keys"):
            env_file = project_root / ".env"

            try:
                # Read existing .env file
                env_content = ""
                if env_file.exists():
                    env_content = env_file.read_text()

                # Update keys
                if new_openai_key:
                    if "OPENAI_API_KEY=" in env_content:
                        # Replace existing
                        lines = env_content.split('\n')
                        for i, line in enumerate(lines):
                            if line.startswith("OPENAI_API_KEY="):
                                lines[i] = f"OPENAI_API_KEY={new_openai_key}"
                        env_content = '\n'.join(lines)
                    else:
                        # Add new
                        env_content += f"\nOPENAI_API_KEY={new_openai_key}"

                if new_serper_key:
                    if "SERPER_API_KEY=" in env_content:
                        # Replace existing
                        lines = env_content.split('\n')
                        for i, line in enumerate(lines):
                            if line.startswith("SERPER_API_KEY="):
                                lines[i] = f"SERPER_API_KEY={new_serper_key}"
                        env_content = '\n'.join(lines)
                    else:
                        # Add new
                        env_content += f"\nSERPER_API_KEY={new_serper_key}"

                # Write back to file
                env_file.write_text(env_content)

                st.success("API keys updated! Please restart the application.")

            except Exception as e:
                st.error(f"Error updating API keys: {str(e)}")

    with tab2:
        st.markdown("### 🔧 Framework Settings")

        # Model settings
        st.markdown("#### 🤖 Model Configuration")

        model_provider = st.selectbox(
            "LLM Provider",
            ["OpenAI", "Anthropic", "Local Model"],
            index=0
        )

        if model_provider == "OpenAI":
            model_name = st.selectbox(
                "Model",
                ["gpt-4", "gpt-4-turbo", "gpt-3.5-turbo"],
                index=0
            )

        temperature = st.slider("Temperature", 0.0, 1.0, 0.7, 0.1)
        max_tokens = st.slider("Max Tokens", 100, 4000, 1000, 100)

        # Search settings
        st.markdown("#### 🔍 Search Configuration")

        max_search_results = st.slider("Max Search Results", 5, 50, 10)
        search_timeout = st.slider("Search Timeout (seconds)", 10, 120, 30)

        # RAG settings
        st.markdown("#### 📚 RAG Configuration")

        chunk_size = st.slider("Chunk Size", 100, 2000, 500, 100)
        chunk_overlap = st.slider("Chunk Overlap", 0, 500, 50, 25)
        top_k_results = st.slider("Top-K Results", 1, 20, 5)

        if st.button("💾 Save Settings"):
            st.success("Settings saved! (Note: This is a demo - actual persistence would be implemented)")

    with tab3:
        st.markdown("### 📊 Data Management")

        # Analysis history management
        st.markdown("#### 📋 Analysis History")

        if st.session_state.analysis_history:
            st.write(f"Total analyses: {len(st.session_state.analysis_history)}")

            col1, col2 = st.columns(2)

            with col1:
                if st.button("📥 Export History"):
                    history_json = json.dumps(st.session_state.analysis_history, indent=2)
                    st.download_button(
                        label="💾 Download JSON",
                        data=history_json,
                        file_name=f"analysis_history_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                        mime="application/json"
                    )

            with col2:
                if st.button("🗑️ Clear History", type="secondary"):
                    if st.button("⚠️ Confirm Clear", type="secondary"):
                        st.session_state.analysis_history = []
                        st.success("Analysis history cleared!")
                        st.rerun()
        else:
            st.info("No analysis history available")

        # Index management
        st.markdown("#### 📚 Index Management")

        col1, col2 = st.columns(2)

        with col1:
            if st.button("🔄 Rebuild Index"):
                st.info("Index rebuild functionality would be implemented here")

        with col2:
            if st.button("🗑️ Clear Index"):
                st.warning("Index clearing functionality would be implemented here")

        # System info
        st.markdown("#### ℹ️ System Information")

        system_info = {
            "Python Version": sys.version.split()[0],
            "Streamlit Version": st.__version__,
            "Project Root": str(project_root),
            "Current Time": datetime.now().isoformat()
        }

        for key, value in system_info.items():
            st.write(f"**{key}:** {value}")

def main():
    """Main application function"""
    initialize_session_state()
    display_header()
    
    # Get current page from sidebar or session state
    if 'current_page' not in st.session_state:
        st.session_state.current_page = sidebar_navigation()
    else:
        page = sidebar_navigation()
        if page != st.session_state.current_page:
            st.session_state.current_page = page
    
    # Route to appropriate page
    if st.session_state.current_page == "🏠 Home":
        home_page()
    elif st.session_state.current_page == "🌍 Geopolitical Analysis":
        geopolitical_page()
    elif st.session_state.current_page == "🔒 Cyber Threat Intelligence":
        cti_page()
    elif st.session_state.current_page == "📚 Document Management":
        document_management_page()
    elif st.session_state.current_page == "📊 Analytics Dashboard":
        analytics_dashboard_page()
    elif st.session_state.current_page == "⚙️ Settings":
        settings_page()

if __name__ == "__main__":
    main()
